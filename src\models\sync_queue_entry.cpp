#include "sync_queue_entry.h"
#include <QJsonDocument>
#include <QDebug>

SyncQueueEntry::SyncQueueEntry()
    : m_id(-1)
    , m_operationType(CreateAttempt)
    , m_status(Pending)
    , m_createdAt(QDateTime::currentDateTime())
    , m_retryCount(0)
{
}

SyncQueueEntry::SyncQueueEntry(OperationType type, const QJsonObject &data)
    : m_id(-1)
    , m_operationType(type)
    , m_data(data)
    , m_status(Pending)
    , m_createdAt(QDateTime::currentDateTime())
    , m_retryCount(0)
{
}

SyncQueueEntry::SyncQueueEntry(int id, OperationType type, const QJsonObject &data, 
                               SyncStatus status, const QDateTime &createdAt)
    : m_id(id)
    , m_operationType(type)
    , m_data(data)
    , m_status(status)
    , m_createdAt(createdAt)
    , m_retryCount(0)
{
}

int SyncQueueEntry::id() const
{
    return m_id;
}

SyncQueueEntry::OperationType SyncQueueEntry::operationType() const
{
    return m_operationType;
}

QJsonObject SyncQueueEntry::data() const
{
    return m_data;
}

SyncQueueEntry::SyncStatus SyncQueueEntry::status() const
{
    return m_status;
}

QDateTime SyncQueueEntry::createdAt() const
{
    return m_createdAt;
}

QDateTime SyncQueueEntry::lastAttempt() const
{
    return m_lastAttempt;
}

int SyncQueueEntry::retryCount() const
{
    return m_retryCount;
}

void SyncQueueEntry::setId(int id)
{
    m_id = id;
}

void SyncQueueEntry::setStatus(SyncStatus status)
{
    m_status = status;
}

void SyncQueueEntry::setLastAttempt(const QDateTime &dateTime)
{
    m_lastAttempt = dateTime;
}

void SyncQueueEntry::incrementRetryCount()
{
    m_retryCount++;
}

void SyncQueueEntry::resetRetryCount()
{
    m_retryCount = 0;
}

QString SyncQueueEntry::operationTypeString() const
{
    switch (m_operationType) {
    case CreateAttempt:
        return "create_attempt";
    case UpdateAthlete:
        return "update_athlete";
    case UpdateCompetition:
        return "update_competition";
    default:
        return "unknown";
    }
}

QString SyncQueueEntry::statusString() const
{
    switch (m_status) {
    case Pending:
        return "pending";
    case InProgress:
        return "in_progress";
    case Completed:
        return "completed";
    case Failed:
        return "failed";
    default:
        return "unknown";
    }
}

bool SyncQueueEntry::isRetryable() const
{
    return m_status == Failed && !hasExceededMaxRetries();
}

bool SyncQueueEntry::hasExceededMaxRetries() const
{
    return m_retryCount >= MAX_RETRY_COUNT;
}

QJsonObject SyncQueueEntry::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["operationType"] = operationTypeString();
    json["data"] = m_data;
    json["status"] = statusString();
    json["createdAt"] = m_createdAt.toString(Qt::ISODate);
    json["lastAttempt"] = m_lastAttempt.toString(Qt::ISODate);
    json["retryCount"] = m_retryCount;
    return json;
}

SyncQueueEntry SyncQueueEntry::fromJson(const QJsonObject &json)
{
    SyncQueueEntry entry;

    // Validate required fields
    if (!json.contains("operationType") || !json.contains("data")) {
        qWarning() << "SyncQueueEntry::fromJson: Missing required fields";
        return entry; // Return default entry
    }

    entry.m_id = json["id"].toInt(-1);

    // Parse operation type with validation
    QString opTypeStr = json["operationType"].toString();
    if (opTypeStr == "create_attempt") {
        entry.m_operationType = CreateAttempt;
    } else if (opTypeStr == "update_athlete") {
        entry.m_operationType = UpdateAthlete;
    } else if (opTypeStr == "update_competition") {
        entry.m_operationType = UpdateCompetition;
    } else {
        qWarning() << "SyncQueueEntry::fromJson: Unknown operation type:" << opTypeStr;
        entry.m_operationType = CreateAttempt; // Default fallback
    }

    entry.m_data = json["data"].toObject();
    if (entry.m_data.isEmpty()) {
        qWarning() << "SyncQueueEntry::fromJson: Empty data object";
    }

    // Parse status with validation
    QString statusStr = json["status"].toString("pending");
    if (statusStr == "pending") {
        entry.m_status = Pending;
    } else if (statusStr == "in_progress") {
        entry.m_status = InProgress;
    } else if (statusStr == "completed") {
        entry.m_status = Completed;
    } else if (statusStr == "failed") {
        entry.m_status = Failed;
    } else {
        qWarning() << "SyncQueueEntry::fromJson: Unknown status:" << statusStr;
        entry.m_status = Pending; // Default fallback
    }

    // Parse timestamps with validation
    QString createdAtStr = json["createdAt"].toString();
    if (!createdAtStr.isEmpty()) {
        entry.m_createdAt = QDateTime::fromString(createdAtStr, Qt::ISODate);
        if (!entry.m_createdAt.isValid()) {
            qWarning() << "SyncQueueEntry::fromJson: Invalid createdAt timestamp:" << createdAtStr;
            entry.m_createdAt = QDateTime::currentDateTime();
        }
    } else {
        entry.m_createdAt = QDateTime::currentDateTime();
    }

    QString lastAttemptStr = json["lastAttempt"].toString();
    if (!lastAttemptStr.isEmpty()) {
        entry.m_lastAttempt = QDateTime::fromString(lastAttemptStr, Qt::ISODate);
        if (!entry.m_lastAttempt.isValid()) {
            qWarning() << "SyncQueueEntry::fromJson: Invalid lastAttempt timestamp:" << lastAttemptStr;
            entry.m_lastAttempt = QDateTime();
        }
    }

    entry.m_retryCount = qMax(0, json["retryCount"].toInt(0)); // Ensure non-negative

    return entry;
}

bool SyncQueueEntry::operator==(const SyncQueueEntry &other) const
{
    return m_id == other.m_id &&
           m_operationType == other.m_operationType &&
           m_data == other.m_data &&
           m_status == other.m_status;
}

bool SyncQueueEntry::operator!=(const SyncQueueEntry &other) const
{
    return !(*this == other);
}
