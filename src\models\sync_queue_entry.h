#ifndef SYNC_QUEUE_ENTRY_H
#define SYNC_QUEUE_ENTRY_H

#include <QObject>
#include <QJsonObject>
#include <QDateTime>
#include <QMetaType>

/**
 * @brief SyncQueueEntry represents a single operation in the synchronization queue
 * 
 * This class encapsulates data for operations that need to be synchronized
 * with the server, including the operation type, data payload, status, and
 * retry information.
 */
class SyncQueueEntry
{
    Q_GADGET

public:
    enum OperationType {
        CreateAttempt,
        UpdateAthlete,
        UpdateCompetition
    };
    Q_ENUM(OperationType)
    
    enum SyncStatus {
        Pending,     // 待同步
        InProgress,  // 同步中
        Completed,   // 已完成
        Failed       // 同步失败
    };
    Q_ENUM(SyncStatus)

    // Constructors
    SyncQueueEntry();
    SyncQueueEntry(OperationType type, const QJsonObject &data);
    SyncQueueEntry(int id, OperationType type, const QJsonObject &data, 
                   SyncStatus status, const QDateTime &createdAt);

    // Getters
    int id() const;
    OperationType operationType() const;
    QJsonObject data() const;
    SyncStatus status() const;
    QDateTime createdAt() const;
    QDateTime lastAttempt() const;
    int retryCount() const;

    // Setters
    void setId(int id);
    void setStatus(SyncStatus status);
    void setLastAttempt(const QDateTime &dateTime);
    void incrementRetryCount();
    void resetRetryCount();

    // Utility methods
    QString operationTypeString() const;
    QString statusString() const;
    bool isRetryable() const;
    bool hasExceededMaxRetries() const;
    
    // Serialization
    QJsonObject toJson() const;
    static SyncQueueEntry fromJson(const QJsonObject &json);
    
    // Operators
    bool operator==(const SyncQueueEntry &other) const;
    bool operator!=(const SyncQueueEntry &other) const;

private:
    int m_id;
    OperationType m_operationType;
    QJsonObject m_data;
    SyncStatus m_status;
    QDateTime m_createdAt;
    QDateTime m_lastAttempt;
    int m_retryCount;
    
    static const int MAX_RETRY_COUNT = 5;
};

Q_DECLARE_METATYPE(SyncQueueEntry)
Q_DECLARE_METATYPE(SyncQueueEntry::OperationType)
Q_DECLARE_METATYPE(SyncQueueEntry::SyncStatus)

#endif // SYNC_QUEUE_ENTRY_H
