#ifndef SYNC_STATUS_INDICATOR_H
#define SYNC_STATUS_INDICATOR_H

#include <QWidget>
#include <QTimer>
#include <QPainter>
#include <QMouseEvent>

/**
 * @brief SyncStatusIndicator displays the current synchronization status
 * 
 * This widget provides a visual indicator of the sync status with different
 * colors and animations. It shows tooltips with detailed status information
 * and can be integrated into status bars or toolbars.
 */
class SyncStatusIndicator : public QWidget
{
    Q_OBJECT

public:
    enum SyncStatus {
        Synchronized,    // 已同步 - 绿色
        Syncing,        // 同步中 - 黄色动画
        Offline,        // 离线 - 灰色
        Error           // 错误 - 红色
    };

    explicit SyncStatusIndicator(QWidget *parent = nullptr);
    ~SyncStatusIndicator();
    
    // Status management
    void setStatus(SyncStatus status);
    SyncStatus status() const;
    
    // Status information
    void setMessage(const QString &message);
    void setPendingCount(int count);
    void setLastSyncTime(const QDateTime &dateTime);
    
    // Appearance
    void setIconSize(int size);
    int iconSize() const;

public slots:
    void onSyncStarted();
    void onSyncCompleted(bool success, int processedCount);
    void onSyncProgress(int completed, int total);
    void onSyncError(const QString &message);
    void onNetworkStatusChanged(bool isOnline);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void enterEvent(QEnterEvent *event) override;
    void leaveEvent(QEvent *event) override;
    QSize sizeHint() const override;
    QSize minimumSizeHint() const override;

private slots:
    void updateAnimation();

private:
    void drawStatusIcon(QPainter &painter);
    void showStatusTooltip();
    QColor getStatusColor() const;
    QString getStatusText() const;
    QString formatTooltipText() const;
    
    SyncStatus m_status;
    QString m_message;
    int m_pendingCount;
    QDateTime m_lastSyncTime;
    int m_iconSize;
    
    // Animation
    QTimer *m_animationTimer;
    int m_animationFrame;
    bool m_isAnimating;
    
    // Progress tracking
    int m_syncProgress;
    int m_syncTotal;
    
    static const int DEFAULT_ICON_SIZE = 16;
    static const int ANIMATION_INTERVAL = 200; // milliseconds
    static const int MAX_ANIMATION_FRAMES = 8;
};

#endif // SYNC_STATUS_INDICATOR_H
