#include <QtTest>
#include <QObject>
#include <QTemporaryDir>
#include <QFileInfo>
#include <QDir>
#include <QSignalSpy>
#include <QApplication>

#include "../../src/ui/export_dialog.h"
#include "../../src/utils/export_manager.h"

class TestExportIntegration : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Integration test cases
    void testExportDialogCreation();
    void testExportDialogUIInteraction();
    void testCompleteExportWorkflow();
    void testExportDialogWithExportManager();
    void testFileSystemIntegration();
    void testErrorHandlingIntegration();
    void testProgressReporting();
    void testUserCancellation();

private:
    QTemporaryDir *m_tempDir;
    QString m_validPath;
    int m_testCompetitionId;
};

void TestExportIntegration::initTestCase()
{
    // Ensure QApplication exists for UI tests
    if (!QApplication::instance()) {
        int argc = 0;
        char **argv = nullptr;
        new QApplication(argc, argv);
    }
    
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    m_validPath = m_tempDir->path();
    m_testCompetitionId = 1;
}

void TestExportIntegration::cleanupTestCase()
{
    delete m_tempDir;
}

void TestExportIntegration::init()
{
    // Clean up any existing files
    QDir exportDir(m_validPath);
    QStringList files = exportDir.entryList(QDir::Files);
    for (const QString &file : files) {
        exportDir.remove(file);
    }
}

void TestExportIntegration::cleanup()
{
    // Cleanup after each test
}

void TestExportIntegration::testExportDialogCreation()
{
    ExportDialog dialog(m_testCompetitionId);
    
    QVERIFY(dialog.windowTitle().contains("导出"));
    QVERIFY(dialog.isModal());
    
    // Dialog should be properly initialized
    QVERIFY(dialog.size().width() > 0);
    QVERIFY(dialog.size().height() > 0);
}

void TestExportIntegration::testExportDialogUIInteraction()
{
    ExportDialog dialog(m_testCompetitionId);
    
    // Test signal connections
    QSignalSpy startedSpy(&dialog, &ExportDialog::exportStarted);
    QSignalSpy finishedSpy(&dialog, &ExportDialog::exportFinished);
    
    QVERIFY(startedSpy.isValid());
    QVERIFY(finishedSpy.isValid());
    
    // Show dialog (non-modal for testing)
    dialog.show();
    QVERIFY(dialog.isVisible());
    
    // Test progress updates
    dialog.onExportProgress(1, 2);
    dialog.onExportProgress(2, 2);
    
    // Test completion
    dialog.onExportCompleted(true, "Test completion message");
    
    dialog.close();
}

void TestExportIntegration::testCompleteExportWorkflow()
{
    ExportManager manager;
    
    QSignalSpy progressSpy(&manager, &ExportManager::progressUpdated);
    QSignalSpy completionSpy(&manager, &ExportManager::exportCompleted);
    
    // Start complete export workflow
    manager.startExport(m_testCompetitionId, m_validPath, true, true);
    
    // Wait for completion
    QVERIFY(completionSpy.wait(10000)); // 10 second timeout for integration test
    
    // Verify signals were emitted
    QVERIFY(progressSpy.count() > 0);
    QCOMPARE(completionSpy.count(), 1);
    
    // Check completion result
    QList<QVariant> arguments = completionSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    QString message = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(!message.isEmpty());
    
    // Verify files were created
    QDir exportDir(m_validPath);
    QStringList pdfFiles = exportDir.entryList(QStringList() << "*.pdf", QDir::Files);
    QStringList csvFiles = exportDir.entryList(QStringList() << "*.csv", QDir::Files);
    
    QVERIFY(pdfFiles.size() > 0);
    QVERIFY(csvFiles.size() > 0);
}

void TestExportIntegration::testExportDialogWithExportManager()
{
    ExportDialog dialog(m_testCompetitionId);
    
    QSignalSpy dialogStartedSpy(&dialog, &ExportDialog::exportStarted);
    QSignalSpy dialogFinishedSpy(&dialog, &ExportDialog::exportFinished);
    
    // Simulate dialog workflow
    dialog.show();
    
    // Simulate user setting export path
    // Note: In a real test, we would interact with UI elements
    // For now, we'll test the underlying functionality
    
    // Test progress reporting
    dialog.onExportProgress(0, 2);
    dialog.onExportProgress(1, 2);
    dialog.onExportProgress(2, 2);
    
    // Test successful completion
    QString successMessage = QString("导出成功完成！\n\n已生成以下文件：\n• test.pdf\n• test.csv\n\n保存位置: %1")
                            .arg(m_validPath);
    dialog.onExportCompleted(true, successMessage);
    
    QCOMPARE(dialogFinishedSpy.count(), 1);
    
    QList<QVariant> arguments = dialogFinishedSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    QString message = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(message.contains("成功"));
    
    dialog.close();
}

void TestExportIntegration::testFileSystemIntegration()
{
    ExportManager manager;
    
    // Test with various file system scenarios
    
    // 1. Normal export
    QString normalPath = QDir(m_validPath).filePath("normal");
    QDir().mkpath(normalPath);
    
    QSignalSpy completionSpy(&manager, &ExportManager::exportCompleted);
    manager.startExport(m_testCompetitionId, normalPath, true, false);
    
    QVERIFY(completionSpy.wait(5000));
    
    QList<QVariant> arguments = completionSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    QVERIFY(success);
    
    // Verify file exists
    QDir normalDir(normalPath);
    QStringList pdfFiles = normalDir.entryList(QStringList() << "*.pdf", QDir::Files);
    QVERIFY(pdfFiles.size() > 0);
    
    // 2. Test file permissions
    QFileInfo pdfFile(normalDir.filePath(pdfFiles.first()));
    QVERIFY(pdfFile.isReadable());
    QVERIFY(pdfFile.size() > 0);
}

void TestExportIntegration::testErrorHandlingIntegration()
{
    ExportManager manager;
    QSignalSpy errorSpy(&manager, &ExportManager::exportError);
    QSignalSpy completionSpy(&manager, &ExportManager::exportCompleted);
    
    // Test with invalid path
    QString invalidPath = "/completely/invalid/path/that/does/not/exist";
    manager.startExport(m_testCompetitionId, invalidPath, true, true);
    
    // Should get error or failed completion
    QVERIFY(errorSpy.wait(2000) || completionSpy.wait(2000));
    
    if (completionSpy.count() > 0) {
        QList<QVariant> arguments = completionSpy.takeFirst();
        bool success = arguments.at(0).toBool();
        QVERIFY(!success); // Should fail
    }
    
    // Test dialog error handling
    ExportDialog dialog(m_testCompetitionId);
    dialog.show();
    
    QString errorMessage = "测试错误消息";
    dialog.onExportCompleted(false, errorMessage);
    
    // Dialog should handle error gracefully
    QVERIFY(dialog.isVisible()); // Should still be visible for user to see error
    
    dialog.close();
}

void TestExportIntegration::testProgressReporting()
{
    ExportManager manager;
    ExportDialog dialog(m_testCompetitionId);
    
    QSignalSpy managerProgressSpy(&manager, &ExportManager::progressUpdated);
    QSignalSpy dialogProgressSpy(&dialog, &ExportDialog::exportStarted);
    
    // Connect manager to dialog for progress updates
    connect(&manager, &ExportManager::progressUpdated,
            &dialog, &ExportDialog::onExportProgress);
    connect(&manager, &ExportManager::exportCompleted,
            &dialog, &ExportDialog::onExportCompleted);
    
    dialog.show();
    
    // Start export
    manager.startExport(m_testCompetitionId, m_validPath, true, true);
    
    QSignalSpy completionSpy(&manager, &ExportManager::exportCompleted);
    QVERIFY(completionSpy.wait(10000));
    
    // Should have received progress updates
    QVERIFY(managerProgressSpy.count() > 0);
    
    // Verify progress values are reasonable
    for (const QList<QVariant> &signal : managerProgressSpy) {
        int current = signal.at(0).toInt();
        int total = signal.at(1).toInt();
        
        QVERIFY(current >= 0);
        QVERIFY(total > 0);
        QVERIFY(current <= total);
    }
    
    dialog.close();
}

void TestExportIntegration::testUserCancellation()
{
    ExportDialog dialog(m_testCompetitionId);
    
    dialog.show();
    
    // Test cancellation before export starts
    dialog.reject(); // Simulate user clicking cancel
    
    // Dialog should close
    QVERIFY(!dialog.isVisible());
    
    // Test cancellation during export (if implemented)
    ExportDialog dialog2(m_testCompetitionId);
    dialog2.show();
    
    // Start export
    dialog2.onExportProgress(1, 2); // Simulate export in progress
    
    // Try to cancel - should show message about export in progress
    // This would require actual UI interaction in a full test
    
    dialog2.close();
}

QTEST_MAIN(TestExportIntegration)
#include "test_export_integration.moc"
