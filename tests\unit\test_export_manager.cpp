#include <QtTest>
#include <QObject>
#include <QTemporaryDir>
#include <QFileInfo>
#include <QDir>
#include <QSignalSpy>

#include "../../src/utils/export_manager.h"

class TestExportManager : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Test cases
    void testConstructor();
    void testValidExportPath();
    void testInvalidExportPath();
    void testExportPdfOnly();
    void testExportCsvOnly();
    void testExportBothFormats();
    void testExportWithInvalidCompetitionId();
    void testExportProgressSignals();
    void testExportCompletionSignals();
    void testConcurrentExportPrevention();
    void testTimestampedFilenames();

private:
    ExportManager *m_exportManager;
    QTemporaryDir *m_tempDir;
    QString m_validPath;
    int m_testCompetitionId;
};

void TestExportManager::initTestCase()
{
    // Initialize test environment
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    m_validPath = m_tempDir->path();
    m_testCompetitionId = 1;
}

void TestExportManager::cleanupTestCase()
{
    delete m_tempDir;
}

void TestExportManager::init()
{
    m_exportManager = new ExportManager(this);
    QVERIFY(m_exportManager != nullptr);
}

void TestExportManager::cleanup()
{
    delete m_exportManager;
    m_exportManager = nullptr;
}

void TestExportManager::testConstructor()
{
    QVERIFY(m_exportManager != nullptr);
    
    // Test that components are initialized
    // Note: We can't directly test private members, but we can test behavior
}

void TestExportManager::testValidExportPath()
{
    QSignalSpy progressSpy(m_exportManager, &ExportManager::progressUpdated);
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Test export to valid path
    m_exportManager->startExport(m_testCompetitionId, m_validPath, true, true);
    
    // Wait for completion signal
    QVERIFY(completionSpy.wait(5000)); // 5 second timeout
    
    // Check that completion signal was emitted
    QCOMPARE(completionSpy.count(), 1);
    
    // Check completion signal arguments
    QList<QVariant> arguments = completionSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    QString message = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(!message.isEmpty());
}

void TestExportManager::testInvalidExportPath()
{
    QSignalSpy errorSpy(m_exportManager, &ExportManager::exportError);
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    QString invalidPath = "/invalid/nonexistent/path";
    
    // Test export to invalid path
    m_exportManager->startExport(m_testCompetitionId, invalidPath, true, false);
    
    // Should get error signal quickly
    QVERIFY(errorSpy.wait(1000) || completionSpy.wait(1000));
    
    if (completionSpy.count() > 0) {
        QList<QVariant> arguments = completionSpy.takeFirst();
        bool success = arguments.at(0).toBool();
        QVERIFY(!success); // Should fail
    }
}

void TestExportManager::testExportPdfOnly()
{
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Test PDF-only export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, true, false);
    
    QVERIFY(completionSpy.wait(5000));
    
    QList<QVariant> arguments = completionSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    QString message = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(message.contains("PDF") || message.contains("pdf"));
}

void TestExportManager::testExportCsvOnly()
{
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Test CSV-only export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, false, true);
    
    QVERIFY(completionSpy.wait(5000));
    
    QList<QVariant> arguments = completionSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    QString message = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(message.contains("CSV") || message.contains("csv"));
}

void TestExportManager::testExportBothFormats()
{
    QSignalSpy progressSpy(m_exportManager, &ExportManager::progressUpdated);
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Test both formats export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, true, true);
    
    QVERIFY(completionSpy.wait(5000));
    
    // Should have progress updates for both formats
    QVERIFY(progressSpy.count() >= 2);
    
    QList<QVariant> arguments = completionSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    QString message = arguments.at(1).toString();
    
    QVERIFY(success);
    QVERIFY(message.contains("PDF") || message.contains("CSV"));
}

void TestExportManager::testExportWithInvalidCompetitionId()
{
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Test with invalid competition ID
    m_exportManager->startExport(-1, m_validPath, true, true);
    
    QVERIFY(completionSpy.wait(3000));
    
    QList<QVariant> arguments = completionSpy.takeFirst();
    bool success = arguments.at(0).toBool();
    
    // Should fail with invalid competition ID
    QVERIFY(!success);
}

void TestExportManager::testExportProgressSignals()
{
    QSignalSpy progressSpy(m_exportManager, &ExportManager::progressUpdated);
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Start export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, true, true);
    
    QVERIFY(completionSpy.wait(5000));
    
    // Should have received progress signals
    QVERIFY(progressSpy.count() > 0);
    
    // Check progress signal format
    for (const QList<QVariant> &signal : progressSpy) {
        QCOMPARE(signal.size(), 2);
        int current = signal.at(0).toInt();
        int total = signal.at(1).toInt();
        
        QVERIFY(current >= 0);
        QVERIFY(total > 0);
        QVERIFY(current <= total);
    }
}

void TestExportManager::testExportCompletionSignals()
{
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Start export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, true, false);
    
    QVERIFY(completionSpy.wait(5000));
    
    // Should have exactly one completion signal
    QCOMPARE(completionSpy.count(), 1);
    
    QList<QVariant> arguments = completionSpy.takeFirst();
    QCOMPARE(arguments.size(), 2);
    
    bool success = arguments.at(0).toBool();
    QString message = arguments.at(1).toString();
    
    QVERIFY(!message.isEmpty());
}

void TestExportManager::testConcurrentExportPrevention()
{
    QSignalSpy errorSpy(m_exportManager, &ExportManager::exportError);
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Start first export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, true, false);
    
    // Immediately try to start second export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, false, true);
    
    // Should get error for concurrent export attempt
    QVERIFY(errorSpy.wait(1000));
    
    // Wait for first export to complete
    QVERIFY(completionSpy.wait(5000));
    
    // Should have one error and one completion
    QCOMPARE(errorSpy.count(), 1);
    QCOMPARE(completionSpy.count(), 1);
}

void TestExportManager::testTimestampedFilenames()
{
    QSignalSpy completionSpy(m_exportManager, &ExportManager::exportCompleted);
    
    // Start export
    m_exportManager->startExport(m_testCompetitionId, m_validPath, true, true);
    
    QVERIFY(completionSpy.wait(5000));
    
    // Check that files were created with timestamps
    QDir exportDir(m_validPath);
    QStringList pdfFiles = exportDir.entryList(QStringList() << "*.pdf", QDir::Files);
    QStringList csvFiles = exportDir.entryList(QStringList() << "*.csv", QDir::Files);
    
    QVERIFY(pdfFiles.size() > 0);
    QVERIFY(csvFiles.size() > 0);
    
    // Check filename format (should contain timestamp)
    for (const QString &filename : pdfFiles) {
        QVERIFY(filename.contains("_"));
        QVERIFY(filename.endsWith(".pdf"));
    }
    
    for (const QString &filename : csvFiles) {
        QVERIFY(filename.contains("_"));
        QVERIFY(filename.endsWith(".csv"));
    }
}

QTEST_MAIN(TestExportManager)
#include "test_export_manager.moc"
