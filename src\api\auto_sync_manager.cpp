#include "auto_sync_manager.h"
#include "network_status_detector.h"
#include "sync_queue_manager.h"
#include "api_client.h"
#include <QMutexLocker>
#include <QDebug>
#include <stdexcept>

AutoSyncManager::AutoSyncManager(QObject *parent)
    : QObject(parent)
    , m_networkDetector(nullptr)
    , m_queueManager(nullptr)
    , m_apiClient(nullptr)
    , m_syncTimer(new QTimer(this))
    , m_isSyncing(false)
    , m_autoSyncEnabled(false)
    , m_syncInterval(DEFAULT_SYNC_INTERVAL)
    , m_maxRetries(DEFAULT_MAX_RETRIES)
    , m_batchSize(DEFAULT_BATCH_SIZE)
    , m_processedCount(0)
    , m_totalCount(0)
{
    // Configure sync timer
    m_syncTimer->setSingleShot(false);
    connect(m_syncTimer, &QTimer::timeout, this, &AutoSyncManager::performScheduledSync);
    
    qDebug() << "AutoSyncManager initialized";
}

AutoSyncManager::~AutoSyncManager()
{
    stopAutoSync();
    qDebug() << "AutoSyncManager destroyed";
}

void AutoSyncManager::startAutoSync()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_autoSyncEnabled) {
        return;
    }
    
    m_autoSyncEnabled = true;
    m_syncTimer->start(m_syncInterval * 1000);
    
    // Perform immediate sync if online
    if (m_networkDetector && m_networkDetector->isOnline()) {
        QTimer::singleShot(0, this, &AutoSyncManager::forceSyncNow);
    }
    
    qDebug() << "Auto sync started with interval:" << m_syncInterval << "seconds";
}

void AutoSyncManager::stopAutoSync()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_autoSyncEnabled) {
        return;
    }
    
    m_autoSyncEnabled = false;
    m_syncTimer->stop();
    
    qDebug() << "Auto sync stopped";
}

void AutoSyncManager::forceSyncNow()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isSyncing) {
        qDebug() << "Sync already in progress, skipping force sync";
        return;
    }
    
    if (!m_networkDetector || !m_networkDetector->isOnline()) {
        qDebug() << "Network offline, cannot force sync";
        return;
    }
    
    QTimer::singleShot(0, this, &AutoSyncManager::processSyncBatch);
}

void AutoSyncManager::setSyncInterval(int seconds)
{
    QMutexLocker locker(&m_mutex);
    
    if (seconds > 0) {
        m_syncInterval = seconds;
        
        if (m_autoSyncEnabled) {
            m_syncTimer->setInterval(seconds * 1000);
        }
        
        qDebug() << "Sync interval updated to:" << seconds << "seconds";
    }
}

void AutoSyncManager::setMaxRetries(int retries)
{
    QMutexLocker locker(&m_mutex);
    
    if (retries >= 0) {
        m_maxRetries = retries;
        qDebug() << "Max retries updated to:" << retries;
    }
}

void AutoSyncManager::setBatchSize(int size)
{
    QMutexLocker locker(&m_mutex);
    
    if (size > 0) {
        m_batchSize = size;
        qDebug() << "Batch size updated to:" << size;
    }
}

void AutoSyncManager::setNetworkDetector(NetworkStatusDetector *detector)
{
    if (m_networkDetector) {
        disconnect(m_networkDetector, nullptr, this, nullptr);
    }
    
    m_networkDetector = detector;
    
    if (m_networkDetector) {
        connect(m_networkDetector, &NetworkStatusDetector::statusChanged,
                this, &AutoSyncManager::onNetworkStatusChanged);
        qDebug() << "NetworkStatusDetector connected to AutoSyncManager";
    }
}

void AutoSyncManager::setSyncQueueManager(SyncQueueManager *queueManager)
{
    m_queueManager = queueManager;
    if (m_queueManager) {
        qDebug() << "SyncQueueManager connected to AutoSyncManager";
    }
}

void AutoSyncManager::setApiClient(ApiClient *apiClient)
{
    m_apiClient = apiClient;
    if (m_apiClient) {
        qDebug() << "ApiClient connected to AutoSyncManager";
    }
}

bool AutoSyncManager::isSyncing() const
{
    QMutexLocker locker(&m_mutex);
    return m_isSyncing;
}

bool AutoSyncManager::isAutoSyncEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_autoSyncEnabled;
}

int AutoSyncManager::pendingOperationsCount() const
{
    if (!m_queueManager) {
        return 0;
    }
    return m_queueManager->pendingCount();
}

void AutoSyncManager::onNetworkStatusChanged(bool isOnline)
{
    qDebug() << "Network status changed to:" << (isOnline ? "online" : "offline");
    
    if (isOnline && m_autoSyncEnabled && !m_isSyncing) {
        // Network restored, trigger sync
        QTimer::singleShot(1000, this, &AutoSyncManager::forceSyncNow); // Small delay to ensure stability
    }
}

void AutoSyncManager::performScheduledSync()
{
    if (!m_autoSyncEnabled || m_isSyncing) {
        return;
    }
    
    if (!m_networkDetector || !m_networkDetector->isOnline()) {
        qDebug() << "Skipping scheduled sync - network offline";
        return;
    }
    
    if (!m_queueManager || m_queueManager->pendingCount() == 0) {
        qDebug() << "Skipping scheduled sync - no pending operations";
        return;
    }
    
    processSyncBatch();
}

void AutoSyncManager::processSyncBatch()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isSyncing) {
        return;
    }
    
    if (!m_queueManager || !m_apiClient) {
        qWarning() << "Cannot sync - missing required components";
        return;
    }
    
    QList<SyncQueueEntry> pendingOperations = m_queueManager->getPendingOperations();
    if (pendingOperations.isEmpty()) {
        qDebug() << "No pending operations to sync";
        return;
    }
    
    m_isSyncing = true;
    m_processedCount = 0;
    m_totalCount = qMin(pendingOperations.size(), m_batchSize);
    m_currentBatch = pendingOperations.mid(0, m_batchSize);
    
    emit syncStarted();
    qDebug() << "Starting sync batch with" << m_currentBatch.size() << "operations";
    
    processNextBatch();
}

void AutoSyncManager::processNextBatch()
{
    if (m_currentBatch.isEmpty()) {
        // Batch complete
        m_isSyncing = false;
        emit syncCompleted(true, m_processedCount);
        qDebug() << "Sync batch completed. Processed:" << m_processedCount << "operations";
        return;
    }
    
    SyncQueueEntry operation = m_currentBatch.takeFirst();
    
    // Mark as in progress
    if (m_queueManager) {
        m_queueManager->markAsInProgress(operation.id());
    }
    
    bool success = processSyncOperation(operation);
    
    if (success) {
        if (m_queueManager) {
            m_queueManager->markAsCompleted(operation.id());
        }
        m_processedCount++;
    } else {
        handleSyncError(operation, "Sync operation failed");
    }
    
    updateSyncProgress();
    
    // Continue with next operation
    QTimer::singleShot(100, this, &AutoSyncManager::processNextBatch); // Small delay between operations
}

bool AutoSyncManager::processSyncOperation(const SyncQueueEntry &operation)
{
    if (!m_apiClient) {
        qWarning() << "AutoSyncManager: ApiClient not available for sync operation";
        return false;
    }

    // Validate operation data before processing
    if (operation.data().isEmpty()) {
        qWarning() << "AutoSyncManager: Empty data for operation ID:" << operation.id();
        return false;
    }

    qDebug() << "Processing sync operation:" << operation.operationTypeString()
             << "ID:" << operation.id() << "Retry count:" << operation.retryCount();

    // Check if operation has exceeded retry limits
    if (operation.hasExceededMaxRetries()) {
        qWarning() << "AutoSyncManager: Operation" << operation.id()
                   << "has exceeded maximum retry count";
        return false;
    }

    try {
        // TODO: Implement actual API calls based on operation type
        // This would include:
        // - Mapping operation type to specific API endpoint
        // - Handling async responses with proper timeout
        // - Validating server response format
        // - Implementing exponential backoff for retries

        // For now, simulate processing with validation
        switch (operation.operationType()) {
        case SyncQueueEntry::CreateAttempt:
            // Validate required fields for attempt creation
            if (!operation.data().contains("athleteId") || !operation.data().contains("height")) {
                qWarning() << "AutoSyncManager: Missing required fields for CreateAttempt";
                return false;
            }
            break;
        case SyncQueueEntry::UpdateAthlete:
            if (!operation.data().contains("id")) {
                qWarning() << "AutoSyncManager: Missing athlete ID for UpdateAthlete";
                return false;
            }
            break;
        case SyncQueueEntry::UpdateCompetition:
            if (!operation.data().contains("id")) {
                qWarning() << "AutoSyncManager: Missing competition ID for UpdateCompetition";
                return false;
            }
            break;
        }

        // Simulate successful processing
        qDebug() << "AutoSyncManager: Successfully processed operation" << operation.id();
        return true;

    } catch (const std::exception &e) {
        qWarning() << "AutoSyncManager: Exception processing operation" << operation.id()
                   << ":" << e.what();
        return false;
    }
}

void AutoSyncManager::handleSyncError(const SyncQueueEntry &operation, const QString &error)
{
    qWarning() << "Sync error for operation" << operation.id() << ":" << error;
    
    if (m_queueManager) {
        m_queueManager->markAsFailed(operation.id(), error);
    }
    
    emit syncError(error);
}

void AutoSyncManager::updateSyncProgress()
{
    emit syncProgress(m_processedCount, m_totalCount);
}
