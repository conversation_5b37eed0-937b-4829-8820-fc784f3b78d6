#include <QtTest>
#include <QSignalSpy>
#include <QNetworkAccessManager>
#include <QTimer>
#include "api/network_status_detector.h"

class TestNetworkDetector : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // Basic functionality tests
    void testInitialization();
    void testStartStopMonitoring();
    void testSetTestEndpoint();
    void testSetCheckInterval();
    
    // Network status tests
    void testNetworkStatusSignals();
    void testConnectionTest();
    void testOfflineDetection();
    
    // Configuration tests
    void testEndpointConfiguration();
    void testIntervalConfiguration();

private:
    NetworkStatusDetector *m_detector;
};

void TestNetworkDetector::initTestCase()
{
    // Setup for entire test class
}

void TestNetworkDetector::init()
{
    m_detector = new NetworkStatusDetector(this);
}

void TestNetworkDetector::cleanup()
{
    delete m_detector;
    m_detector = nullptr;
}

void TestNetworkDetector::cleanupTestCase()
{
    // Cleanup for entire test class
}

void TestNetworkDetector::testInitialization()
{
    QVERIFY(m_detector != nullptr);
    QVERIFY(!m_detector->isOnline()); // Should start offline
    QCOMPARE(m_detector->checkInterval(), 30); // Default interval
}

void TestNetworkDetector::testStartStopMonitoring()
{
    // Test starting monitoring
    m_detector->startMonitoring();
    
    // Test stopping monitoring
    m_detector->stopMonitoring();
    
    // Should be able to start/stop multiple times
    m_detector->startMonitoring();
    m_detector->startMonitoring(); // Should not cause issues
    m_detector->stopMonitoring();
    m_detector->stopMonitoring(); // Should not cause issues
}

void TestNetworkDetector::testSetTestEndpoint()
{
    QUrl validEndpoint("https://example.com/test");
    m_detector->setTestEndpoint(validEndpoint);
    
    QUrl invalidEndpoint("invalid-url");
    m_detector->setTestEndpoint(invalidEndpoint); // Should handle gracefully
}

void TestNetworkDetector::testSetCheckInterval()
{
    // Test valid intervals
    m_detector->setCheckInterval(60);
    QCOMPARE(m_detector->checkInterval(), 60);
    
    m_detector->setCheckInterval(10);
    QCOMPARE(m_detector->checkInterval(), 10);
    
    // Test invalid intervals
    m_detector->setCheckInterval(0);
    QCOMPARE(m_detector->checkInterval(), 10); // Should remain unchanged
    
    m_detector->setCheckInterval(-5);
    QCOMPARE(m_detector->checkInterval(), 10); // Should remain unchanged
}

void TestNetworkDetector::testNetworkStatusSignals()
{
    QSignalSpy statusChangedSpy(m_detector, &NetworkStatusDetector::statusChanged);
    QSignalSpy connectionRestoredSpy(m_detector, &NetworkStatusDetector::connectionRestored);
    QSignalSpy connectionLostSpy(m_detector, &NetworkStatusDetector::connectionLost);
    
    // Verify signals are properly defined
    QVERIFY(statusChangedSpy.isValid());
    QVERIFY(connectionRestoredSpy.isValid());
    QVERIFY(connectionLostSpy.isValid());
}

void TestNetworkDetector::testConnectionTest()
{
    // Test manual connection test
    m_detector->testConnection();
    
    // Should not crash or cause issues
    QVERIFY(true);
}

void TestNetworkDetector::testOfflineDetection()
{
    // Set invalid endpoint to simulate offline
    m_detector->setTestEndpoint(QUrl("http://invalid.nonexistent.domain.test"));
    
    QSignalSpy statusChangedSpy(m_detector, &NetworkStatusDetector::statusChanged);
    
    m_detector->testConnection();
    
    // Wait a bit for network request to complete
    QTest::qWait(2000);
    
    // Should detect as offline (though this depends on actual network conditions)
    // In a real test environment, you'd mock the network manager
}

void TestNetworkDetector::testEndpointConfiguration()
{
    QUrl endpoint1("https://api1.example.com/health");
    QUrl endpoint2("https://api2.example.com/status");
    
    m_detector->setTestEndpoint(endpoint1);
    m_detector->setTestEndpoint(endpoint2);
    
    // Should handle endpoint changes gracefully
    QVERIFY(true);
}

void TestNetworkDetector::testIntervalConfiguration()
{
    m_detector->setCheckInterval(5);
    m_detector->startMonitoring();
    
    // Change interval while monitoring
    m_detector->setCheckInterval(15);
    QCOMPARE(m_detector->checkInterval(), 15);
    
    m_detector->stopMonitoring();
}

QTEST_MAIN(TestNetworkDetector)
#include "test_network_detector.moc"
