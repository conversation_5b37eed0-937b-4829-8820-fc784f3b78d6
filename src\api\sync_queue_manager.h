#ifndef SYNC_QUEUE_MANAGER_H
#define SYNC_QUEUE_MANAGER_H

#include <QObject>
#include <QQueue>
#include <QDateTime>
#include <QMutex>
#include "models/sync_queue_entry.h"

class DatabaseManager;

/**
 * @brief SyncQueueManager manages the synchronization queue operations
 * 
 * This class provides thread-safe management of sync operations including
 * adding new operations, retrieving pending operations, and updating
 * operation status. It integrates with DatabaseManager for persistence.
 */
class SyncQueueManager : public QObject
{
    Q_OBJECT

public:
    explicit SyncQueueManager(QObject *parent = nullptr);
    ~SyncQueueManager();

    // Queue operations
    bool addOperation(const SyncQueueEntry &operation);
    QList<SyncQueueEntry> getPendingOperations();
    QList<SyncQueueEntry> getFailedOperations();
    bool markAsCompleted(int operationId);
    bool markAsFailed(int operationId, const QString &error);
    bool markAsInProgress(int operationId);
    
    // Queue statistics
    int pendingCount() const;
    int failedCount() const;
    int totalCount() const;
    QDateTime lastSyncTime() const;
    
    // Queue management
    bool clearCompleted();
    bool clearFailed();
    bool retryFailed();
    
    // Database integration
    void setDatabaseManager(DatabaseManager *dbManager);

signals:
    void operationAdded(const SyncQueueEntry &operation);
    void operationCompleted(int operationId);
    void operationFailed(int operationId, const QString &error);
    void queueStatsChanged(int pending, int failed, int total);

private slots:
    void updateQueueStats();

private:
    bool updateOperationStatus(int operationId, SyncQueueEntry::SyncStatus status, 
                              const QString &error = QString());
    void loadPendingOperations();
    
    DatabaseManager *m_dbManager;
    QQueue<SyncQueueEntry> m_pendingQueue;
    mutable QMutex m_mutex;
    
    // Statistics cache
    int m_pendingCount;
    int m_failedCount;
    int m_totalCount;
    QDateTime m_lastSyncTime;
};

#endif // SYNC_QUEUE_MANAGER_H
