#include "sync_queue_manager.h"
#include "persistence/database_manager.h"
#include <QMutexLocker>
#include <QDebug>

SyncQueueManager::SyncQueueManager(QObject *parent)
    : QObject(parent)
    , m_dbManager(nullptr)
    , m_pendingCount(0)
    , m_failedCount(0)
    , m_totalCount(0)
{
    qDebug() << "SyncQueueManager initialized";
}

SyncQueueManager::~SyncQueueManager()
{
    qDebug() << "SyncQueueManager destroyed";
}

bool SyncQueueManager::addOperation(const SyncQueueEntry &operation)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManager not set";
        return false;
    }
    
    // Add to database
    if (!m_dbManager->addToSyncQueue(operation)) {
        qWarning() << "Failed to add operation to database";
        return false;
    }
    
    // Add to in-memory queue
    m_pendingQueue.enqueue(operation);
    
    emit operationAdded(operation);
    updateQueueStats();
    
    qDebug() << "Added sync operation:" << operation.operationTypeString();
    return true;
}

QList<SyncQueueEntry> SyncQueueManager::getPendingOperations()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManager not set";
        return QList<SyncQueueEntry>();
    }
    
    return m_dbManager->getPendingSyncEntries();
}

QList<SyncQueueEntry> SyncQueueManager::getFailedOperations()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManager not set";
        return QList<SyncQueueEntry>();
    }
    
    return m_dbManager->getFailedSyncEntries();
}

bool SyncQueueManager::markAsCompleted(int operationId)
{
    bool success = updateOperationStatus(operationId, SyncQueueEntry::Completed);
    if (success) {
        m_lastSyncTime = QDateTime::currentDateTime();
        emit operationCompleted(operationId);
        updateQueueStats();
        qDebug() << "Marked operation as completed:" << operationId;
    }
    return success;
}

bool SyncQueueManager::markAsFailed(int operationId, const QString &error)
{
    bool success = updateOperationStatus(operationId, SyncQueueEntry::Failed, error);
    if (success) {
        emit operationFailed(operationId, error);
        updateQueueStats();
        qDebug() << "Marked operation as failed:" << operationId << "Error:" << error;
    }
    return success;
}

bool SyncQueueManager::markAsInProgress(int operationId)
{
    bool success = updateOperationStatus(operationId, SyncQueueEntry::InProgress);
    if (success) {
        updateQueueStats();
        qDebug() << "Marked operation as in progress:" << operationId;
    }
    return success;
}

int SyncQueueManager::pendingCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_pendingCount;
}

int SyncQueueManager::failedCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_failedCount;
}

int SyncQueueManager::totalCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_totalCount;
}

QDateTime SyncQueueManager::lastSyncTime() const
{
    QMutexLocker locker(&m_mutex);
    return m_lastSyncTime;
}

bool SyncQueueManager::clearCompleted()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManager not set";
        return false;
    }
    
    bool success = m_dbManager->clearCompletedSyncEntries();
    if (success) {
        updateQueueStats();
        qDebug() << "Cleared completed sync operations";
    }
    return success;
}

bool SyncQueueManager::clearFailed()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManager not set";
        return false;
    }
    
    bool success = m_dbManager->clearFailedSyncEntries();
    if (success) {
        updateQueueStats();
        qDebug() << "Cleared failed sync operations";
    }
    return success;
}

bool SyncQueueManager::retryFailed()
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManager not set";
        return false;
    }
    
    QList<SyncQueueEntry> failedEntries = m_dbManager->getFailedSyncEntries();
    bool allSuccess = true;
    
    for (const auto &entry : failedEntries) {
        if (entry.isRetryable()) {
            bool success = m_dbManager->updateSyncStatus(entry.id(), SyncQueueEntry::Pending);
            if (!success) {
                allSuccess = false;
                qWarning() << "Failed to retry operation:" << entry.id();
            }
        }
    }
    
    if (allSuccess) {
        updateQueueStats();
        qDebug() << "Retried" << failedEntries.size() << "failed operations";
    }
    
    return allSuccess;
}

void SyncQueueManager::setDatabaseManager(DatabaseManager *dbManager)
{
    QMutexLocker locker(&m_mutex);
    m_dbManager = dbManager;
    
    if (m_dbManager) {
        loadPendingOperations();
        updateQueueStats();
        qDebug() << "DatabaseManager set for SyncQueueManager";
    }
}

void SyncQueueManager::updateQueueStats()
{
    if (!m_dbManager) {
        return;
    }
    
    int pending = m_dbManager->getPendingSyncCount();
    int failed = m_dbManager->getFailedSyncCount();
    int total = m_dbManager->getTotalSyncCount();
    
    bool changed = (pending != m_pendingCount || failed != m_failedCount || total != m_totalCount);
    
    m_pendingCount = pending;
    m_failedCount = failed;
    m_totalCount = total;
    
    if (changed) {
        emit queueStatsChanged(pending, failed, total);
    }
}

bool SyncQueueManager::updateOperationStatus(int operationId, SyncQueueEntry::SyncStatus status, 
                                            const QString &error)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManager not set";
        return false;
    }
    
    return m_dbManager->updateSyncStatus(operationId, status);
}

void SyncQueueManager::loadPendingOperations()
{
    if (!m_dbManager) {
        return;
    }
    
    QList<SyncQueueEntry> pendingEntries = m_dbManager->getPendingSyncEntries();
    
    m_pendingQueue.clear();
    for (const auto &entry : pendingEntries) {
        m_pendingQueue.enqueue(entry);
    }
    
    qDebug() << "Loaded" << pendingEntries.size() << "pending operations";
}
