#ifndef AUTO_SYNC_MANAGER_H
#define AUTO_SYNC_MANAGER_H

#include <QObject>
#include <QTimer>
#include <QMutex>
#include "models/sync_queue_entry.h"

class NetworkStatusDetector;
class SyncQueueManager;
class ApiClient;

/**
 * @brief AutoSyncManager coordinates automatic synchronization of offline data
 * 
 * This class manages the automatic synchronization process by monitoring
 * network status and processing sync queue operations when connectivity
 * is available. It handles batch processing, retry logic, and error recovery.
 */
class AutoSyncManager : public QObject
{
    Q_OBJECT

public:
    explicit AutoSyncManager(QObject *parent = nullptr);
    ~AutoSyncManager();

    // Sync control
    void startAutoSync();
    void stopAutoSync();
    void forceSyncNow();
    
    // Sync configuration
    void setSyncInterval(int seconds);
    void setMaxRetries(int retries);
    void setBatchSize(int size);
    
    // Component integration
    void setNetworkDetector(NetworkStatusDetector *detector);
    void setSyncQueueManager(SyncQueueManager *queueManager);
    void setApiClient(ApiClient *apiClient);
    
    // Status query
    bool isSyncing() const;
    bool isAutoSyncEnabled() const;
    int pendingOperationsCount() const;

signals:
    void syncStarted();
    void syncProgress(int completed, int total);
    void syncCompleted(bool success, int processedCount);
    void syncError(const QString &message);

private slots:
    void onNetworkStatusChanged(bool isOnline);
    void performScheduledSync();
    void processSyncBatch();

private:
    void processNextBatch();
    bool processSyncOperation(const SyncQueueEntry &operation);
    void handleSyncError(const SyncQueueEntry &operation, const QString &error);
    void updateSyncProgress();
    
    NetworkStatusDetector *m_networkDetector;
    SyncQueueManager *m_queueManager;
    ApiClient *m_apiClient;
    
    QTimer *m_syncTimer;
    QMutex m_mutex;
    
    bool m_isSyncing;
    bool m_autoSyncEnabled;
    int m_syncInterval;
    int m_maxRetries;
    int m_batchSize;
    
    // Current sync session state
    QList<SyncQueueEntry> m_currentBatch;
    int m_processedCount;
    int m_totalCount;
    
    static const int DEFAULT_SYNC_INTERVAL = 60; // seconds
    static const int DEFAULT_MAX_RETRIES = 3;
    static const int DEFAULT_BATCH_SIZE = 10;
};

#endif // AUTO_SYNC_MANAGER_H
