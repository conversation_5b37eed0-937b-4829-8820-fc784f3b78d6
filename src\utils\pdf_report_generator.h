#ifndef PDF_REPORT_GENERATOR_H
#define PDF_REPORT_GENERATOR_H

#include <QObject>
#include <QString>
#include <QPrinter>
#include <QPainter>
#include <QFont>
#include <QRect>
#include <QList>

class DatabaseManager;

struct CompetitionData {
    int id;
    QString name;
    QString date;
    QString venue;
    QString category;
};

struct AthleteResult {
    int rank;
    QString name;
    QString team;
    int bibNumber;
    QString bestHeight;
    QString personalBest;
    QString seasonBest;
    QStringList attemptResults; // Results for each height
};

class PdfReportGenerator : public QObject
{
    Q_OBJECT

public:
    explicit PdfReportGenerator(QObject *parent = nullptr);
    ~PdfReportGenerator();

    bool generateReport(int competitionId, const QString &outputPath);
    QString lastError() const { return m_lastError; }

private:
    bool loadCompetitionData(int competitionId);
    bool setupPrinter(const QString &outputPath);
    void drawHeader(QPainter &painter, const QRect &pageRect);
    void drawCompetitionInfo(QPainter &painter, const QRect &pageRect, int &currentY);
    void drawResultsTable(QPainter &painter, const QRect &pageRect, int &currentY);
    void drawTableHeader(QPainter &painter, const QRect &tableRect, int &currentY);
    void drawAthleteRow(QPainter &painter, const QRect &tableRect, 
                       const AthleteResult &athlete, int &currentY, bool isEven);
    void drawFooter(QPainter &painter, const QRect &pageRect, int pageNumber);
    
    // Helper methods
    QFont getTitleFont() const;
    QFont getHeaderFont() const;
    QFont getBodyFont() const;
    QFont getSmallFont() const;
    
    int getLineHeight(const QFont &font) const;
    QString formatDateTime(const QString &dateStr) const;
    bool needsNewPage(int currentY, int requiredHeight, const QRect &pageRect) const;
    void startNewPage(QPainter &painter, const QRect &pageRect, int &currentY, int &pageNumber);
    
    // Data members
    DatabaseManager *m_databaseManager;
    QPrinter *m_printer;
    
    CompetitionData m_competitionData;
    QList<AthleteResult> m_athleteResults;
    QStringList m_heights;
    
    QString m_lastError;
    
    // Layout constants
    static const int MARGIN = 50;
    static const int LINE_SPACING = 5;
    static const int TABLE_ROW_HEIGHT = 25;
    static const int HEADER_HEIGHT = 80;
    static const int FOOTER_HEIGHT = 30;
};

#endif // PDF_REPORT_GENERATOR_H
