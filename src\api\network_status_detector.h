#ifndef NETWORK_STATUS_DETECTOR_H
#define NETWORK_STATUS_DETECTOR_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QTimer>
#include <QUrl>
#include <QNetworkReply>

/**
 * @brief NetworkStatusDetector monitors network connectivity status
 * 
 * This class provides real-time network status detection by periodically
 * testing connectivity to a configured endpoint. It emits signals when
 * network status changes between online and offline states.
 */
class NetworkStatusDetector : public QObject
{
    Q_OBJECT

public:
    explicit NetworkStatusDetector(QObject *parent = nullptr);
    ~NetworkStatusDetector();

    // Network status query
    bool isOnline() const;
    
    // Monitoring control
    void startMonitoring();
    void stopMonitoring();
    
    // Connection testing
    void testConnection();
    void setTestEndpoint(const QUrl &endpoint);
    
    // Configuration
    void setCheckInterval(int seconds);
    int checkInterval() const;

signals:
    void statusChanged(bool isOnline);
    void connectionRestored();
    void connectionLost();

private slots:
    void checkNetworkStatus();
    void onTestRequestFinished();

private:
    void updateStatus(bool isOnline);
    void performConnectivityTest();
    
    QNetworkAccessManager *m_manager;
    QTimer *m_checkTimer;
    QUrl m_testEndpoint;
    QNetworkReply *m_currentReply;
    
    bool m_isOnline;
    int m_checkInterval;
    bool m_isMonitoring;
    
    static const int DEFAULT_CHECK_INTERVAL = 30; // seconds
    static const int REQUEST_TIMEOUT = 5000; // milliseconds
};

#endif // NETWORK_STATUS_DETECTOR_H
