#include "network_status_detector.h"
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QDebug>
#include <QSettings>

NetworkStatusDetector::NetworkStatusDetector(QObject *parent)
    : QObject(parent)
    , m_manager(new QNetworkAccessManager(this))
    , m_checkTimer(new QTimer(this))
    , m_currentReply(nullptr)
    , m_isOnline(false)
    , m_checkInterval(DEFAULT_CHECK_INTERVAL)
    , m_isMonitoring(false)
{
    // Load test endpoint from configuration
    QSettings settings;
    QString defaultEndpoint = "https://api.example.com/health";
    m_testEndpoint = QUrl(settings.value("network/testEndpoint", defaultEndpoint).toString());
    
    // Configure timer
    m_checkTimer->setSingleShot(false);
    connect(m_checkTimer, &QTimer::timeout, this, &NetworkStatusDetector::checkNetworkStatus);
    
    // Configure network manager
    m_manager->setTransferTimeout(REQUEST_TIMEOUT);
    
    qDebug() << "NetworkStatusDetector initialized with endpoint:" << m_testEndpoint.toString();
}

NetworkStatusDetector::~NetworkStatusDetector()
{
    stopMonitoring();
    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
    }
}

bool NetworkStatusDetector::isOnline() const
{
    return m_isOnline;
}

void NetworkStatusDetector::startMonitoring()
{
    if (m_isMonitoring) {
        return;
    }
    
    m_isMonitoring = true;
    m_checkTimer->start(m_checkInterval * 1000);
    
    // Perform immediate test
    testConnection();
    
    qDebug() << "Network monitoring started with interval:" << m_checkInterval << "seconds";
}

void NetworkStatusDetector::stopMonitoring()
{
    if (!m_isMonitoring) {
        return;
    }
    
    m_isMonitoring = false;
    m_checkTimer->stop();
    
    if (m_currentReply) {
        m_currentReply->abort();
        m_currentReply->deleteLater();
        m_currentReply = nullptr;
    }
    
    qDebug() << "Network monitoring stopped";
}

void NetworkStatusDetector::testConnection()
{
    performConnectivityTest();
}

void NetworkStatusDetector::setTestEndpoint(const QUrl &endpoint)
{
    if (endpoint.isValid()) {
        m_testEndpoint = endpoint;
        
        // Save to configuration
        QSettings settings;
        settings.setValue("network/testEndpoint", endpoint.toString());
        
        qDebug() << "Test endpoint updated to:" << endpoint.toString();
    } else {
        qWarning() << "Invalid test endpoint provided:" << endpoint.toString();
    }
}

void NetworkStatusDetector::setCheckInterval(int seconds)
{
    if (seconds > 0) {
        m_checkInterval = seconds;
        
        if (m_isMonitoring) {
            m_checkTimer->setInterval(seconds * 1000);
        }
        
        qDebug() << "Check interval updated to:" << seconds << "seconds";
    } else {
        qWarning() << "Invalid check interval:" << seconds;
    }
}

int NetworkStatusDetector::checkInterval() const
{
    return m_checkInterval;
}

void NetworkStatusDetector::checkNetworkStatus()
{
    if (!m_currentReply) {
        performConnectivityTest();
    }
}

void NetworkStatusDetector::onTestRequestFinished()
{
    QNetworkReply *reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        qWarning() << "NetworkStatusDetector: Invalid reply object in finished slot";
        return;
    }

    // Ensure we're handling the current reply to avoid race conditions
    if (m_currentReply != reply) {
        qDebug() << "NetworkStatusDetector: Received finished signal for non-current reply";
        reply->deleteLater();
        return;
    }

    bool isNowOnline = (reply->error() == QNetworkReply::NoError);

    if (reply->error() != QNetworkReply::NoError) {
        qDebug() << "Network test failed:" << reply->errorString()
                 << "HTTP status:" << reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
    } else {
        qDebug() << "Network test successful - HTTP status:"
                 << reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
    }

    updateStatus(isNowOnline);

    // Clean up - order matters for thread safety
    m_currentReply = nullptr;
    reply->deleteLater();
}

void NetworkStatusDetector::updateStatus(bool isOnline)
{
    bool wasOnline = m_isOnline;
    m_isOnline = isOnline;
    
    if (wasOnline != isOnline) {
        emit statusChanged(isOnline);
        
        if (isOnline) {
            qDebug() << "Network connection restored";
            emit connectionRestored();
        } else {
            qDebug() << "Network connection lost";
            emit connectionLost();
        }
    }
}

void NetworkStatusDetector::performConnectivityTest()
{
    if (m_currentReply) {
        // Test already in progress
        return;
    }
    
    QNetworkRequest request(m_testEndpoint);
    request.setHeader(QNetworkRequest::UserAgentHeader, "HighJumpScorer/1.0");
    request.setRawHeader("Cache-Control", "no-cache");
    
    m_currentReply = m_manager->head(request);
    connect(m_currentReply, &QNetworkReply::finished, 
            this, &NetworkStatusDetector::onTestRequestFinished);
    
    qDebug() << "Performing connectivity test to:" << m_testEndpoint.toString();
}
