#ifndef EXPORT_DIALOG_H
#define EXPORT_DIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QCheckBox>
#include <QPushButton>
#include <QLineEdit>
#include <QProgressBar>
#include <QFileDialog>
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>
#include <QGroupBox>

class ExportManager;

class ExportDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ExportDialog(int competitionId, QWidget *parent = nullptr);
    ~ExportDialog();

signals:
    void exportStarted();
    void exportFinished(bool success, const QString &message);

public slots:
    void onExportProgress(int current, int total);
    void onExportCompleted(bool success, const QString &message);

private slots:
    void onBrowseClicked();
    void onExportClicked();
    void onCancelClicked();
    void updateExportButton();

private:
    void setupUI();
    void setupConnections();
    QString getDefaultExportPath() const;
    bool validateInputs() const;
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    QGroupBox *m_formatGroup;
    QGroupBox *m_locationGroup;
    QGroupBox *m_progressGroup;
    
    QCheckBox *m_pdfCheckBox;
    QCheckBox *m_csvCheckBox;
    
    QLineEdit *m_pathLineEdit;
    QPushButton *m_browseButton;
    
    QProgressBar *m_progressBar;
    QLabel *m_statusLabel;
    
    QPushButton *m_exportButton;
    QPushButton *m_cancelButton;
    
    // Data
    int m_competitionId;
    ExportManager *m_exportManager;
    bool m_exportInProgress;
};

#endif // EXPORT_DIALOG_H
