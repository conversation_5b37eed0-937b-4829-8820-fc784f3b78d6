#include "sync_status_indicator.h"
#include <QPainter>
#include <QToolTip>
#include <QDateTime>
#include <QDebug>

SyncStatusIndicator::SyncStatusIndicator(QWidget *parent)
    : QWidget(parent)
    , m_status(Offline)
    , m_pendingCount(0)
    , m_iconSize(DEFAULT_ICON_SIZE)
    , m_animationTimer(new QTimer(this))
    , m_animationFrame(0)
    , m_isAnimating(false)
    , m_syncProgress(0)
    , m_syncTotal(0)
{
    setFixedSize(m_iconSize + 4, m_iconSize + 4);
    setToolTip(tr("Sync Status"));

    // Accessibility improvements
    setAccessibleName(tr("Synchronization Status Indicator"));
    setAccessibleDescription(tr("Shows the current status of data synchronization"));

    // Configure animation timer with better performance
    m_animationTimer->setInterval(ANIMATION_INTERVAL);
    m_animationTimer->setTimerType(Qt::CoarseTimer); // Better performance for UI animations
    connect(m_animationTimer, &QTimer::timeout, this, &SyncStatusIndicator::updateAnimation);

    // Enable mouse tracking for better hover effects
    setMouseTracking(true);

    qDebug() << "SyncStatusIndicator initialized with accessibility support";
}

SyncStatusIndicator::~SyncStatusIndicator()
{
    qDebug() << "SyncStatusIndicator destroyed";
}

void SyncStatusIndicator::setStatus(SyncStatus status)
{
    if (m_status == status) {
        return;
    }
    
    SyncStatus oldStatus = m_status;
    m_status = status;
    
    // Handle animation
    if (status == Syncing) {
        if (!m_isAnimating) {
            m_isAnimating = true;
            m_animationTimer->start();
        }
    } else {
        if (m_isAnimating) {
            m_isAnimating = false;
            m_animationTimer->stop();
            m_animationFrame = 0;
        }
    }
    
    update();
    setToolTip(formatTooltipText());
    
    qDebug() << "Sync status changed from" << oldStatus << "to" << status;
}

SyncStatusIndicator::SyncStatus SyncStatusIndicator::status() const
{
    return m_status;
}

void SyncStatusIndicator::setMessage(const QString &message)
{
    m_message = message;
    setToolTip(formatTooltipText());
}

void SyncStatusIndicator::setPendingCount(int count)
{
    m_pendingCount = count;
    setToolTip(formatTooltipText());
}

void SyncStatusIndicator::setLastSyncTime(const QDateTime &dateTime)
{
    m_lastSyncTime = dateTime;
    setToolTip(formatTooltipText());
}

void SyncStatusIndicator::setIconSize(int size)
{
    if (size > 0 && size != m_iconSize) {
        m_iconSize = size;
        setFixedSize(size + 4, size + 4);
        update();
    }
}

int SyncStatusIndicator::iconSize() const
{
    return m_iconSize;
}

void SyncStatusIndicator::onSyncStarted()
{
    setStatus(Syncing);
    setMessage(tr("Synchronizing..."));
    m_syncProgress = 0;
    m_syncTotal = 0;
}

void SyncStatusIndicator::onSyncCompleted(bool success, int processedCount)
{
    if (success) {
        setStatus(Synchronized);
        setMessage(tr("Synchronized %1 operations").arg(processedCount));
        setLastSyncTime(QDateTime::currentDateTime());
    } else {
        setStatus(Error);
        setMessage(tr("Sync failed"));
    }
    
    m_syncProgress = 0;
    m_syncTotal = 0;
}

void SyncStatusIndicator::onSyncProgress(int completed, int total)
{
    m_syncProgress = completed;
    m_syncTotal = total;
    
    if (total > 0) {
        setMessage(tr("Syncing %1/%2 operations").arg(completed).arg(total));
    }
}

void SyncStatusIndicator::onSyncError(const QString &message)
{
    setStatus(Error);
    setMessage(message);
}

void SyncStatusIndicator::onNetworkStatusChanged(bool isOnline)
{
    if (!isOnline) {
        setStatus(Offline);
        setMessage(tr("Network offline"));
    } else if (m_status == Offline) {
        // Only change from offline if we were offline
        setStatus(Synchronized);
        setMessage(tr("Network online"));
    }
}

void SyncStatusIndicator::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    drawStatusIcon(painter);
}

void SyncStatusIndicator::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        showStatusTooltip();
    }
    QWidget::mousePressEvent(event);
}

void SyncStatusIndicator::enterEvent(QEnterEvent *event)
{
    Q_UNUSED(event)
    // Tooltip will be shown automatically
}

void SyncStatusIndicator::leaveEvent(QEvent *event)
{
    Q_UNUSED(event)
    QToolTip::hideText();
}

QSize SyncStatusIndicator::sizeHint() const
{
    return QSize(m_iconSize + 4, m_iconSize + 4);
}

QSize SyncStatusIndicator::minimumSizeHint() const
{
    return QSize(12, 12);
}

void SyncStatusIndicator::updateAnimation()
{
    if (m_isAnimating) {
        m_animationFrame = (m_animationFrame + 1) % MAX_ANIMATION_FRAMES;
        update();
    }
}

void SyncStatusIndicator::drawStatusIcon(QPainter &painter)
{
    QRect iconRect(2, 2, m_iconSize, m_iconSize);
    QColor color = getStatusColor();
    
    painter.setBrush(QBrush(color));
    painter.setPen(QPen(color.darker(150), 1));
    
    if (m_status == Syncing && m_isAnimating) {
        // Draw animated spinning circle
        painter.save();
        painter.translate(iconRect.center());
        painter.rotate(m_animationFrame * 45); // 45 degrees per frame
        
        // Draw spinning arc
        painter.setBrush(Qt::NoBrush);
        painter.setPen(QPen(color, 2, Qt::SolidLine, Qt::RoundCap));
        painter.drawArc(-m_iconSize/2 + 2, -m_iconSize/2 + 2, 
                       m_iconSize - 4, m_iconSize - 4, 0, 270 * 16);
        
        painter.restore();
    } else {
        // Draw solid circle
        painter.drawEllipse(iconRect);
        
        // Add status-specific details
        if (m_status == Error) {
            // Draw X mark
            painter.setPen(QPen(Qt::white, 2));
            int margin = m_iconSize / 4;
            painter.drawLine(iconRect.left() + margin, iconRect.top() + margin,
                           iconRect.right() - margin, iconRect.bottom() - margin);
            painter.drawLine(iconRect.right() - margin, iconRect.top() + margin,
                           iconRect.left() + margin, iconRect.bottom() - margin);
        } else if (m_status == Synchronized) {
            // Draw checkmark
            painter.setPen(QPen(Qt::white, 2, Qt::SolidLine, Qt::RoundCap));
            int margin = m_iconSize / 4;
            QPoint p1(iconRect.left() + margin, iconRect.center().y());
            QPoint p2(iconRect.center().x() - 1, iconRect.bottom() - margin);
            QPoint p3(iconRect.right() - margin, iconRect.top() + margin);
            painter.drawLine(p1, p2);
            painter.drawLine(p2, p3);
        }
    }
}

void SyncStatusIndicator::showStatusTooltip()
{
    QToolTip::showText(mapToGlobal(rect().bottomLeft()), formatTooltipText(), this);
}

QColor SyncStatusIndicator::getStatusColor() const
{
    switch (m_status) {
    case Synchronized:
        return QColor(34, 139, 34);  // Forest Green
    case Syncing:
        return QColor(255, 165, 0);  // Orange
    case Offline:
        return QColor(128, 128, 128); // Gray
    case Error:
        return QColor(220, 20, 60);   // Crimson
    default:
        return QColor(128, 128, 128);
    }
}

QString SyncStatusIndicator::getStatusText() const
{
    switch (m_status) {
    case Synchronized:
        return tr("Synchronized");
    case Syncing:
        return tr("Syncing");
    case Offline:
        return tr("Offline");
    case Error:
        return tr("Error");
    default:
        return tr("Unknown");
    }
}

QString SyncStatusIndicator::formatTooltipText() const
{
    QString tooltip = QString("<b>%1</b>").arg(getStatusText());
    
    if (!m_message.isEmpty()) {
        tooltip += QString("<br/>%1").arg(m_message);
    }
    
    if (m_pendingCount > 0) {
        tooltip += QString("<br/>%1 pending operations").arg(m_pendingCount);
    }
    
    if (m_status == Syncing && m_syncTotal > 0) {
        tooltip += QString("<br/>Progress: %1/%2").arg(m_syncProgress).arg(m_syncTotal);
    }
    
    if (m_lastSyncTime.isValid()) {
        tooltip += QString("<br/>Last sync: %1").arg(m_lastSyncTime.toString("hh:mm:ss"));
    }
    
    return tooltip;
}
