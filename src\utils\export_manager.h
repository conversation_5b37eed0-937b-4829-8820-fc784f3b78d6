#ifndef EXPORT_MANAGER_H
#define EXPORT_MANAGER_H

#include <QObject>
#include <QString>
#include <QThread>
#include <QMutex>
#include <QDateTime>

class PdfReportGenerator;
class CsvExporter;
class DatabaseManager;

class ExportManager : public QObject
{
    Q_OBJECT

public:
    explicit ExportManager(QObject *parent = nullptr);
    ~ExportManager();

    // Main export function
    void startExport(int competitionId, const QString &exportPath, 
                    bool exportPdf, bool exportCsv);

signals:
    void progressUpdated(int current, int total);
    void exportCompleted(bool success, const QString &message);
    void exportError(const QString &error);

private slots:
    void performExport();

private:
    QString generateTimestampedFilename(const QString &baseName, const QString &extension) const;
    bool validateExportPath(const QString &path) const;
    bool checkDataIntegrity(int competitionId) const;
    QString formatSuccessMessage(const QStringList &exportedFiles) const;
    
    // Export components
    PdfReportGenerator *m_pdfGenerator;
    CsvExporter *m_csvExporter;
    DatabaseManager *m_databaseManager;
    
    // Export parameters
    int m_competitionId;
    QString m_exportPath;
    bool m_exportPdf;
    bool m_exportCsv;
    
    // Thread safety
    QMutex m_mutex;
    bool m_exportInProgress;
    
    // Results tracking
    QStringList m_exportedFiles;
    QString m_lastError;
};

#endif // EXPORT_MANAGER_H
