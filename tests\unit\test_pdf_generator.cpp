#include <QtTest>
#include <QObject>
#include <QTemporaryDir>
#include <QFileInfo>
#include <QFile>

#include "../../src/utils/pdf_report_generator.h"

class TestPdfReportGenerator : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Test cases
    void testConstructor();
    void testGenerateReportValidInput();
    void testGenerateReportInvalidCompetitionId();
    void testGenerateReportInvalidPath();
    void testPdfFileCreation();
    void testPdfFileSize();
    void testErrorHandling();
    void testLastErrorMessage();

private:
    PdfReportGenerator *m_generator;
    QTemporaryDir *m_tempDir;
    QString m_validPath;
    int m_testCompetitionId;
};

void TestPdfReportGenerator::initTestCase()
{
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    m_validPath = m_tempDir->path();
    m_testCompetitionId = 1;
}

void TestPdfReportGenerator::cleanupTestCase()
{
    delete m_tempDir;
}

void TestPdfReportGenerator::init()
{
    m_generator = new PdfReportGenerator(this);
    QVERIFY(m_generator != nullptr);
}

void TestPdfReportGenerator::cleanup()
{
    delete m_generator;
    m_generator = nullptr;
}

void TestPdfReportGenerator::testConstructor()
{
    QVERIFY(m_generator != nullptr);
    QVERIFY(m_generator->lastError().isEmpty());
}

void TestPdfReportGenerator::testGenerateReportValidInput()
{
    QString outputPath = QDir(m_validPath).filePath("test_report.pdf");
    
    bool result = m_generator->generateReport(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    QVERIFY(m_generator->lastError().isEmpty());
}

void TestPdfReportGenerator::testGenerateReportInvalidCompetitionId()
{
    QString outputPath = QDir(m_validPath).filePath("test_report_invalid.pdf");
    
    bool result = m_generator->generateReport(-1, outputPath);
    
    // Should handle invalid competition ID gracefully
    // Result depends on implementation - might succeed with placeholder data
    // or fail with appropriate error message
    if (!result) {
        QVERIFY(!m_generator->lastError().isEmpty());
    }
}

void TestPdfReportGenerator::testGenerateReportInvalidPath()
{
    QString invalidPath = "/invalid/nonexistent/directory/test.pdf";
    
    bool result = m_generator->generateReport(m_testCompetitionId, invalidPath);
    
    QVERIFY(!result);
    QVERIFY(!m_generator->lastError().isEmpty());
}

void TestPdfReportGenerator::testPdfFileCreation()
{
    QString outputPath = QDir(m_validPath).filePath("creation_test.pdf");
    
    // Ensure file doesn't exist before test
    QFile::remove(outputPath);
    QVERIFY(!QFile::exists(outputPath));
    
    bool result = m_generator->generateReport(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    QVERIFY(QFile::exists(outputPath));
    
    QFileInfo fileInfo(outputPath);
    QVERIFY(fileInfo.isFile());
    QVERIFY(fileInfo.isReadable());
}

void TestPdfReportGenerator::testPdfFileSize()
{
    QString outputPath = QDir(m_validPath).filePath("size_test.pdf");
    
    bool result = m_generator->generateReport(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    
    QFileInfo fileInfo(outputPath);
    QVERIFY(fileInfo.exists());
    
    // PDF should have reasonable size (at least 1KB for a basic report)
    QVERIFY(fileInfo.size() > 1024);
    
    // PDF should not be excessively large (less than 10MB for a simple report)
    QVERIFY(fileInfo.size() < 10 * 1024 * 1024);
}

void TestPdfReportGenerator::testErrorHandling()
{
    // Test with read-only directory (if possible)
    QString readOnlyPath = QDir(m_validPath).filePath("readonly");
    QDir().mkdir(readOnlyPath);
    
    // Try to make directory read-only
    QFile::setPermissions(readOnlyPath, QFile::ReadOwner | QFile::ReadGroup | QFile::ReadOther);
    
    QString outputPath = QDir(readOnlyPath).filePath("test.pdf");
    
    bool result = m_generator->generateReport(m_testCompetitionId, outputPath);
    
    // Should fail due to permissions
    QVERIFY(!result);
    QVERIFY(!m_generator->lastError().isEmpty());
    
    // Restore permissions for cleanup
    QFile::setPermissions(readOnlyPath, QFile::WriteOwner | QFile::ReadOwner);
}

void TestPdfReportGenerator::testLastErrorMessage()
{
    // Initially no error
    QVERIFY(m_generator->lastError().isEmpty());
    
    // Generate successful report
    QString validPath = QDir(m_validPath).filePath("success_test.pdf");
    bool result = m_generator->generateReport(m_testCompetitionId, validPath);
    
    if (result) {
        // Error should still be empty after successful operation
        QVERIFY(m_generator->lastError().isEmpty());
    }
    
    // Generate report with invalid path
    QString invalidPath = "/invalid/path/test.pdf";
    result = m_generator->generateReport(m_testCompetitionId, invalidPath);
    
    QVERIFY(!result);
    QVERIFY(!m_generator->lastError().isEmpty());
    
    // Error message should be descriptive
    QString errorMsg = m_generator->lastError();
    QVERIFY(errorMsg.length() > 10); // Should be a meaningful message
}

QTEST_MAIN(TestPdfReportGenerator)
#include "test_pdf_generator.moc"
