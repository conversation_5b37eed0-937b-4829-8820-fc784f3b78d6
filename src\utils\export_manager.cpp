#include "export_manager.h"
#include "pdf_report_generator.h"
#include "csv_exporter.h"
#include "../persistence/database_manager.h"

#include <QDir>
#include <QFileInfo>
#include <QStandardPaths>
#include <QTimer>
#include <QDebug>

ExportManager::ExportManager(QObject *parent)
    : QObject(parent)
    , m_pdfGenerator(nullptr)
    , m_csvExporter(nullptr)
    , m_databaseManager(nullptr)
    , m_competitionId(-1)
    , m_exportPdf(false)
    , m_exportCsv(false)
    , m_exportInProgress(false)
{
    // Initialize components
    m_pdfGenerator = new PdfReportGenerator(this);
    m_csvExporter = new CsvExporter(this);
    m_databaseManager = DatabaseManager::instance();
}

ExportManager::~ExportManager()
{
    // Components will be deleted by Qt parent-child system
}

void ExportManager::startExport(int competitionId, const QString &exportPath, 
                               bool exportPdf, bool exportCsv)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_exportInProgress) {
        emit exportError(tr("导出操作正在进行中，请等待完成。"));
        return;
    }
    
    // Validate inputs
    if (!validateExportPath(exportPath)) {
        emit exportError(tr("导出路径无效或无法访问。"));
        return;
    }
    
    if (!exportPdf && !exportCsv) {
        emit exportError(tr("请至少选择一种导出格式。"));
        return;
    }
    
    // Check data integrity
    if (!checkDataIntegrity(competitionId)) {
        emit exportError(tr("比赛数据不完整或无法访问，请检查数据库连接。"));
        return;
    }
    
    // Set export parameters
    m_competitionId = competitionId;
    m_exportPath = exportPath;
    m_exportPdf = exportPdf;
    m_exportCsv = exportCsv;
    m_exportInProgress = true;
    m_exportedFiles.clear();
    m_lastError.clear();
    
    // Start export in next event loop iteration to allow UI updates
    QTimer::singleShot(100, this, &ExportManager::performExport);
}

void ExportManager::performExport()
{
    try {
        int totalSteps = 0;
        if (m_exportPdf) totalSteps++;
        if (m_exportCsv) totalSteps++;
        
        int currentStep = 0;
        
        emit progressUpdated(currentStep, totalSteps);
        
        // Export PDF if requested
        if (m_exportPdf) {
            QString pdfFilename = generateTimestampedFilename("competition_results", "pdf");
            QString pdfPath = QDir(m_exportPath).filePath(pdfFilename);
            
            if (m_pdfGenerator->generateReport(m_competitionId, pdfPath)) {
                m_exportedFiles.append(pdfPath);
                qDebug() << "PDF export successful:" << pdfPath;
            } else {
                m_lastError = tr("PDF导出失败: %1").arg(m_pdfGenerator->lastError());
                throw std::runtime_error(m_lastError.toStdString());
            }
            
            currentStep++;
            emit progressUpdated(currentStep, totalSteps);
        }
        
        // Export CSV if requested
        if (m_exportCsv) {
            QString csvFilename = generateTimestampedFilename("competition_data", "csv");
            QString csvPath = QDir(m_exportPath).filePath(csvFilename);
            
            if (m_csvExporter->exportData(m_competitionId, csvPath)) {
                m_exportedFiles.append(csvPath);
                qDebug() << "CSV export successful:" << csvPath;
            } else {
                m_lastError = tr("CSV导出失败: %1").arg(m_csvExporter->lastError());
                throw std::runtime_error(m_lastError.toStdString());
            }
            
            currentStep++;
            emit progressUpdated(currentStep, totalSteps);
        }
        
        // Export completed successfully
        QString successMessage = formatSuccessMessage(m_exportedFiles);
        
        {
            QMutexLocker locker(&m_mutex);
            m_exportInProgress = false;
        }
        
        emit exportCompleted(true, successMessage);
        
    } catch (const std::exception &e) {
        qCritical() << "Export failed with exception:" << e.what();
        
        {
            QMutexLocker locker(&m_mutex);
            m_exportInProgress = false;
        }
        
        QString errorMessage = m_lastError.isEmpty() ? 
            tr("导出过程中发生未知错误。") : m_lastError;
        
        emit exportCompleted(false, errorMessage);
    }
}

QString ExportManager::generateTimestampedFilename(const QString &baseName, const QString &extension) const
{
    QDateTime now = QDateTime::currentDateTime();
    QString timestamp = now.toString("yyyyMMdd_hhmmss");
    return QString("%1_%2.%3").arg(baseName, timestamp, extension);
}

bool ExportManager::validateExportPath(const QString &path) const
{
    QDir dir(path);
    if (!dir.exists()) {
        qWarning() << "Export path does not exist:" << path;
        return false;
    }
    
    // Test write permissions by creating a temporary file
    QString testFile = dir.filePath("test_write_permission.tmp");
    QFile file(testFile);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "No write permission for export path:" << path;
        return false;
    }
    
    file.close();
    file.remove();
    
    return true;
}

bool ExportManager::checkDataIntegrity(int competitionId) const
{
    if (!m_databaseManager) {
        qCritical() << "DatabaseManager is null";
        return false;
    }
    
    // Check if competition exists
    // Note: This would use the actual DatabaseManager method when available
    // For now, we'll assume the method exists based on the story requirements
    
    try {
        // Placeholder for actual database check
        // auto competition = m_databaseManager->getCompetitionById(competitionId);
        // auto athletes = m_databaseManager->getAthletesByCompetition(competitionId);
        
        // For now, just check if competitionId is valid
        if (competitionId <= 0) {
            qWarning() << "Invalid competition ID:" << competitionId;
            return false;
        }
        
        return true;
        
    } catch (const std::exception &e) {
        qCritical() << "Data integrity check failed:" << e.what();
        return false;
    }
}

QString ExportManager::formatSuccessMessage(const QStringList &exportedFiles) const
{
    if (exportedFiles.isEmpty()) {
        return tr("导出完成，但没有生成文件。");
    }
    
    QString message = tr("导出成功完成！\n\n已生成以下文件：\n");
    
    for (const QString &filePath : exportedFiles) {
        QFileInfo fileInfo(filePath);
        message += QString("• %1\n").arg(fileInfo.fileName());
    }
    
    message += tr("\n保存位置: %1").arg(QDir::toNativeSeparators(m_exportPath));
    
    return message;
}
