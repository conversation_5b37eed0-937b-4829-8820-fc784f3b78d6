#ifndef CSV_EXPORTER_H
#define CSV_EXPORTER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QTextStream>
#include <QFile>

class DatabaseManager;

struct CsvCompetitionData {
    int id;
    QString name;
    QString date;
    QString venue;
    QString category;
    QString status;
};

struct CsvAthleteData {
    int id;
    int competitionId;
    QString name;
    QString team;
    int bibNumber;
    QString personalBest;
    QString seasonBest;
    int finalRank;
    QString bestHeight;
    QString status;
};

struct CsvAttemptData {
    int id;
    int athleteId;
    QString height;
    int attemptNumber;
    QString result;
    QString timestamp;
};

class CsvExporter : public QObject
{
    Q_OBJECT

public:
    explicit CsvExporter(QObject *parent = nullptr);
    ~CsvExporter();

    bool exportData(int competitionId, const QString &outputPath);
    QString lastError() const { return m_lastError; }

private:
    bool loadCompetitionData(int competitionId);
    bool writeCompetitionSheet(QTextStream &stream);
    bool writeAthletesSheet(QTextStream &stream);
    bool writeAttemptsSheet(QTextStream &stream);
    bool writeHeightsSheet(QTextStream &stream);
    bool writeSummarySheet(QTextStream &stream);
    
    // CSV formatting helpers
    QString escapeCsvField(const QString &field) const;
    QString formatCsvRow(const QStringList &fields) const;
    QString formatTimestamp(const QString &timestamp) const;
    
    // Data validation
    bool validateCompetitionData() const;
    bool validateAthleteData() const;
    bool validateAttemptData() const;
    
    // Data members
    DatabaseManager *m_databaseManager;
    
    CsvCompetitionData m_competitionData;
    QList<CsvAthleteData> m_athleteData;
    QList<CsvAttemptData> m_attemptData;
    QStringList m_heights;
    
    QString m_lastError;
    
    // CSV format constants
    static const QString CSV_SEPARATOR;
    static const QString CSV_QUOTE;
    static const QString CSV_NEWLINE;
};

#endif // CSV_EXPORTER_H
