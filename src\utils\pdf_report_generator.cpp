#include "pdf_report_generator.h"
#include "../persistence/database_manager.h"

#include <QPageSize>
#include <QPageLayout>
#include <QFontMetrics>
#include <QDateTime>
#include <QFileInfo>
#include <QDir>
#include <QDebug>

PdfReportGenerator::PdfReportGenerator(QObject *parent)
    : QObject(parent)
    , m_databaseManager(nullptr)
    , m_printer(nullptr)
{
    m_databaseManager = DatabaseManager::instance();
}

PdfReportGenerator::~PdfReportGenerator()
{
    delete m_printer;
}

bool PdfReportGenerator::generateReport(int competitionId, const QString &outputPath)
{
    m_lastError.clear();
    
    try {
        // Load competition data
        if (!loadCompetitionData(competitionId)) {
            return false;
        }
        
        // Setup printer
        if (!setupPrinter(outputPath)) {
            return false;
        }
        
        // Create painter
        QPainter painter(m_printer);
        if (!painter.isActive()) {
            m_lastError = tr("无法初始化PDF绘制器");
            return false;
        }
        
        QRect pageRect = m_printer->pageRect(QPrinter::DevicePixel);
        int currentY = MARGIN;
        int pageNumber = 1;
        
        // Draw report content
        drawHeader(painter, pageRect);
        drawCompetitionInfo(painter, pageRect, currentY);
        drawResultsTable(painter, pageRect, currentY);
        drawFooter(painter, pageRect, pageNumber);
        
        painter.end();
        
        qDebug() << "PDF report generated successfully:" << outputPath;
        return true;
        
    } catch (const std::exception &e) {
        m_lastError = tr("PDF生成过程中发生错误: %1").arg(e.what());
        qCritical() << "PDF generation failed:" << e.what();
        return false;
    }
}

bool PdfReportGenerator::loadCompetitionData(int competitionId)
{
    if (!m_databaseManager) {
        m_lastError = tr("数据库管理器未初始化");
        return false;
    }
    
    try {
        // Note: These methods would be implemented in DatabaseManager
        // For now, we'll create placeholder data based on the story requirements
        
        // Load competition basic info
        m_competitionData.id = competitionId;
        m_competitionData.name = "省级跳高比赛"; // Placeholder
        m_competitionData.date = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        m_competitionData.venue = "体育中心"; // Placeholder
        m_competitionData.category = "男子组"; // Placeholder
        
        // Load heights sequence
        m_heights = {"1.60", "1.65", "1.70", "1.75", "1.80", "1.85", "1.90"}; // Placeholder
        
        // Load athlete results (placeholder data)
        m_athleteResults.clear();
        
        // Create sample athlete results for demonstration
        AthleteResult athlete1;
        athlete1.rank = 1;
        athlete1.name = "张三";
        athlete1.team = "省队";
        athlete1.bibNumber = 101;
        athlete1.bestHeight = "1.85";
        athlete1.personalBest = "1.90";
        athlete1.seasonBest = "1.87";
        athlete1.attemptResults = {"O", "O", "O", "XO", "XXO", "X", "-"};
        m_athleteResults.append(athlete1);
        
        AthleteResult athlete2;
        athlete2.rank = 2;
        athlete2.name = "李四";
        athlete2.team = "市队";
        athlete2.bibNumber = 102;
        athlete2.bestHeight = "1.80";
        athlete2.personalBest = "1.85";
        athlete2.seasonBest = "1.82";
        athlete2.attemptResults = {"O", "O", "O", "O", "XXX", "-", "-"};
        m_athleteResults.append(athlete2);
        
        return true;
        
    } catch (const std::exception &e) {
        m_lastError = tr("加载比赛数据失败: %1").arg(e.what());
        return false;
    }
}

bool PdfReportGenerator::setupPrinter(const QString &outputPath)
{
    // Ensure output directory exists
    QFileInfo fileInfo(outputPath);
    QDir outputDir = fileInfo.dir();
    if (!outputDir.exists()) {
        if (!outputDir.mkpath(".")) {
            m_lastError = tr("无法创建输出目录: %1").arg(outputDir.path());
            return false;
        }
    }
    
    delete m_printer;
    m_printer = new QPrinter(QPrinter::HighResolution);
    
    // Configure printer for PDF output
    m_printer->setOutputFormat(QPrinter::PdfFormat);
    m_printer->setOutputFileName(outputPath);
    m_printer->setPageSize(QPageSize::A4);
    m_printer->setPageOrientation(QPageLayout::Portrait);
    m_printer->setResolution(300); // 300 DPI as specified
    
    // Set margins
    QMarginsF margins(20, 20, 20, 20); // 20mm margins
    m_printer->setPageMargins(margins, QPageLayout::Millimeter);
    
    return true;
}

void PdfReportGenerator::drawHeader(QPainter &painter, const QRect &pageRect)
{
    QFont titleFont = getTitleFont();
    painter.setFont(titleFont);
    
    QRect titleRect(MARGIN, MARGIN, pageRect.width() - 2 * MARGIN, HEADER_HEIGHT);
    painter.drawText(titleRect, Qt::AlignCenter, tr("跳高比赛成绩单"));
    
    // Draw a line under the title
    int lineY = MARGIN + HEADER_HEIGHT - 10;
    painter.drawLine(MARGIN, lineY, pageRect.width() - MARGIN, lineY);
}

void PdfReportGenerator::drawCompetitionInfo(QPainter &painter, const QRect &pageRect, int &currentY)
{
    currentY = MARGIN + HEADER_HEIGHT + 20;
    
    QFont headerFont = getHeaderFont();
    painter.setFont(headerFont);
    
    int lineHeight = getLineHeight(headerFont);
    
    // Competition name
    painter.drawText(MARGIN, currentY, tr("比赛名称: %1").arg(m_competitionData.name));
    currentY += lineHeight + LINE_SPACING;
    
    // Date and venue on same line
    QString dateVenue = tr("比赛日期: %1    比赛地点: %2")
                       .arg(formatDateTime(m_competitionData.date))
                       .arg(m_competitionData.venue);
    painter.drawText(MARGIN, currentY, dateVenue);
    currentY += lineHeight + LINE_SPACING;
    
    // Category
    painter.drawText(MARGIN, currentY, tr("比赛组别: %1").arg(m_competitionData.category));
    currentY += lineHeight + LINE_SPACING * 2;
}

void PdfReportGenerator::drawResultsTable(QPainter &painter, const QRect &pageRect, int &currentY)
{
    QRect tableRect(MARGIN, currentY, pageRect.width() - 2 * MARGIN, 
                   pageRect.height() - currentY - FOOTER_HEIGHT - MARGIN);
    
    int pageNumber = 1;
    
    // Draw table header
    drawTableHeader(painter, tableRect, currentY);
    
    // Draw athlete rows
    for (int i = 0; i < m_athleteResults.size(); ++i) {
        const AthleteResult &athlete = m_athleteResults[i];
        bool isEven = (i % 2) == 0;
        
        // Check if we need a new page
        if (needsNewPage(currentY, TABLE_ROW_HEIGHT, pageRect)) {
            startNewPage(painter, pageRect, currentY, pageNumber);
            drawTableHeader(painter, tableRect, currentY);
        }
        
        drawAthleteRow(painter, tableRect, athlete, currentY, isEven);
    }
}

void PdfReportGenerator::drawTableHeader(QPainter &painter, const QRect &tableRect, int &currentY)
{
    QFont headerFont = getHeaderFont();
    painter.setFont(headerFont);
    
    // Calculate column widths
    int rankWidth = 50;
    int nameWidth = 100;
    int teamWidth = 80;
    int bibWidth = 50;
    int bestWidth = 60;
    int pbWidth = 60;
    int sbWidth = 60;
    int heightsWidth = tableRect.width() - rankWidth - nameWidth - teamWidth - bibWidth - bestWidth - pbWidth - sbWidth;
    
    // Draw header background
    QRect headerRect(tableRect.left(), currentY, tableRect.width(), TABLE_ROW_HEIGHT);
    painter.fillRect(headerRect, QColor(240, 240, 240));
    painter.drawRect(headerRect);
    
    // Draw header text
    int x = tableRect.left();
    int textY = currentY + TABLE_ROW_HEIGHT / 2 + 5;
    
    painter.drawText(QRect(x, currentY, rankWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, tr("名次"));
    x += rankWidth;
    
    painter.drawText(QRect(x, currentY, nameWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, tr("姓名"));
    x += nameWidth;
    
    painter.drawText(QRect(x, currentY, teamWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, tr("单位"));
    x += teamWidth;
    
    painter.drawText(QRect(x, currentY, bibWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, tr("号码"));
    x += bibWidth;
    
    painter.drawText(QRect(x, currentY, bestWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, tr("成绩"));
    x += bestWidth;
    
    painter.drawText(QRect(x, currentY, pbWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, tr("PB"));
    x += pbWidth;
    
    painter.drawText(QRect(x, currentY, sbWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, tr("SB"));
    x += sbWidth;
    
    // Draw height columns
    int heightColWidth = heightsWidth / m_heights.size();
    for (const QString &height : m_heights) {
        painter.drawText(QRect(x, currentY, heightColWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, height);
        x += heightColWidth;
    }
    
    currentY += TABLE_ROW_HEIGHT;
}

void PdfReportGenerator::drawAthleteRow(QPainter &painter, const QRect &tableRect, 
                                      const AthleteResult &athlete, int &currentY, bool isEven)
{
    QFont bodyFont = getBodyFont();
    painter.setFont(bodyFont);
    
    // Draw row background
    QRect rowRect(tableRect.left(), currentY, tableRect.width(), TABLE_ROW_HEIGHT);
    if (isEven) {
        painter.fillRect(rowRect, QColor(250, 250, 250));
    }
    painter.drawRect(rowRect);
    
    // Calculate column widths (same as header)
    int rankWidth = 50;
    int nameWidth = 100;
    int teamWidth = 80;
    int bibWidth = 50;
    int bestWidth = 60;
    int pbWidth = 60;
    int sbWidth = 60;
    int heightsWidth = tableRect.width() - rankWidth - nameWidth - teamWidth - bibWidth - bestWidth - pbWidth - sbWidth;
    
    // Draw athlete data
    int x = tableRect.left();
    
    painter.drawText(QRect(x, currentY, rankWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, QString::number(athlete.rank));
    x += rankWidth;
    
    painter.drawText(QRect(x, currentY, nameWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, athlete.name);
    x += nameWidth;
    
    painter.drawText(QRect(x, currentY, teamWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, athlete.team);
    x += teamWidth;
    
    painter.drawText(QRect(x, currentY, bibWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, QString::number(athlete.bibNumber));
    x += bibWidth;
    
    painter.drawText(QRect(x, currentY, bestWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, athlete.bestHeight);
    x += bestWidth;
    
    painter.drawText(QRect(x, currentY, pbWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, athlete.personalBest);
    x += pbWidth;
    
    painter.drawText(QRect(x, currentY, sbWidth, TABLE_ROW_HEIGHT), Qt::AlignCenter, athlete.seasonBest);
    x += sbWidth;
    
    // Draw attempt results
    int heightColWidth = heightsWidth / m_heights.size();
    for (int i = 0; i < m_heights.size() && i < athlete.attemptResults.size(); ++i) {
        painter.drawText(QRect(x, currentY, heightColWidth, TABLE_ROW_HEIGHT), 
                        Qt::AlignCenter, athlete.attemptResults[i]);
        x += heightColWidth;
    }
    
    currentY += TABLE_ROW_HEIGHT;
}

void PdfReportGenerator::drawFooter(QPainter &painter, const QRect &pageRect, int pageNumber)
{
    QFont smallFont = getSmallFont();
    painter.setFont(smallFont);
    
    int footerY = pageRect.height() - FOOTER_HEIGHT;
    
    // Draw generation timestamp
    QString timestamp = tr("生成时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    painter.drawText(MARGIN, footerY, timestamp);
    
    // Draw page number
    QString pageText = tr("第 %1 页").arg(pageNumber);
    QFontMetrics fm(smallFont);
    int pageTextWidth = fm.horizontalAdvance(pageText);
    painter.drawText(pageRect.width() - MARGIN - pageTextWidth, footerY, pageText);
}

// Helper methods implementation
QFont PdfReportGenerator::getTitleFont() const
{
    QFont font("SimSun", 18, QFont::Bold);
    return font;
}

QFont PdfReportGenerator::getHeaderFont() const
{
    QFont font("SimSun", 12, QFont::Bold);
    return font;
}

QFont PdfReportGenerator::getBodyFont() const
{
    QFont font("SimSun", 10);
    return font;
}

QFont PdfReportGenerator::getSmallFont() const
{
    QFont font("SimSun", 8);
    return font;
}

int PdfReportGenerator::getLineHeight(const QFont &font) const
{
    QFontMetrics fm(font);
    return fm.height();
}

QString PdfReportGenerator::formatDateTime(const QString &dateStr) const
{
    QDateTime dt = QDateTime::fromString(dateStr, "yyyy-MM-dd");
    if (dt.isValid()) {
        return dt.toString("yyyy年MM月dd日");
    }
    return dateStr;
}

bool PdfReportGenerator::needsNewPage(int currentY, int requiredHeight, const QRect &pageRect) const
{
    return (currentY + requiredHeight + FOOTER_HEIGHT + MARGIN) > pageRect.height();
}

void PdfReportGenerator::startNewPage(QPainter &painter, const QRect &pageRect, int &currentY, int &pageNumber)
{
    drawFooter(painter, pageRect, pageNumber);
    m_printer->newPage();
    pageNumber++;
    currentY = MARGIN;
    drawHeader(painter, pageRect);
    currentY = MARGIN + HEADER_HEIGHT + 20;
}
