#include "csv_exporter.h"
#include "../persistence/database_manager.h"

#include <QFileInfo>
#include <QDir>
#include <QDateTime>
#include <QDebug>

// CSV format constants
const QString CsvExporter::CSV_SEPARATOR = ",";
const QString CsvExporter::CSV_QUOTE = "\"";
const QString CsvExporter::CSV_NEWLINE = "\n";

CsvExporter::CsvExporter(QObject *parent)
    : QObject(parent)
    , m_databaseManager(nullptr)
{
    m_databaseManager = DatabaseManager::instance();
}

CsvExporter::~CsvExporter()
{
    // DatabaseManager is a singleton, don't delete
}

bool CsvExporter::exportData(int competitionId, const QString &outputPath)
{
    m_lastError.clear();
    
    try {
        // Load all competition data
        if (!loadCompetitionData(competitionId)) {
            return false;
        }
        
        // Validate data integrity
        if (!validateCompetitionData() || !validateAthleteData() || !validateAttemptData()) {
            return false;
        }
        
        // Ensure output directory exists
        QFileInfo fileInfo(outputPath);
        QDir outputDir = fileInfo.dir();
        if (!outputDir.exists()) {
            if (!outputDir.mkpath(".")) {
                m_lastError = tr("无法创建输出目录: %1").arg(outputDir.path());
                return false;
            }
        }
        
        // Open output file
        QFile file(outputPath);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            m_lastError = tr("无法创建CSV文件: %1").arg(file.errorString());
            return false;
        }
        
        QTextStream stream(&file);
        stream.setEncoding(QStringConverter::Utf8);
        
        // Write CSV header comment
        stream << "# High Jump Competition Data Export" << CSV_NEWLINE;
        stream << "# Generated: " << QDateTime::currentDateTime().toString(Qt::ISODate) << CSV_NEWLINE;
        stream << "# Competition ID: " << competitionId << CSV_NEWLINE;
        stream << CSV_NEWLINE;
        
        // Write different data sections
        if (!writeCompetitionSheet(stream)) return false;
        stream << CSV_NEWLINE;
        
        if (!writeAthletesSheet(stream)) return false;
        stream << CSV_NEWLINE;
        
        if (!writeAttemptsSheet(stream)) return false;
        stream << CSV_NEWLINE;
        
        if (!writeHeightsSheet(stream)) return false;
        stream << CSV_NEWLINE;
        
        if (!writeSummarySheet(stream)) return false;
        
        file.close();
        
        qDebug() << "CSV export completed successfully:" << outputPath;
        return true;
        
    } catch (const std::exception &e) {
        m_lastError = tr("CSV导出过程中发生错误: %1").arg(e.what());
        qCritical() << "CSV export failed:" << e.what();
        return false;
    }
}

bool CsvExporter::loadCompetitionData(int competitionId)
{
    if (!m_databaseManager) {
        m_lastError = tr("数据库管理器未初始化");
        return false;
    }
    
    try {
        // Note: These methods would be implemented in DatabaseManager
        // For now, we'll create placeholder data based on the story requirements
        
        // Load competition basic info
        m_competitionData.id = competitionId;
        m_competitionData.name = "省级跳高比赛";
        m_competitionData.date = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        m_competitionData.venue = "体育中心";
        m_competitionData.category = "男子组";
        m_competitionData.status = "completed";
        
        // Load heights sequence
        m_heights = {"1.60", "1.65", "1.70", "1.75", "1.80", "1.85", "1.90"};
        
        // Load athlete data (placeholder)
        m_athleteData.clear();
        
        CsvAthleteData athlete1;
        athlete1.id = 1;
        athlete1.competitionId = competitionId;
        athlete1.name = "张三";
        athlete1.team = "省队";
        athlete1.bibNumber = 101;
        athlete1.personalBest = "1.90";
        athlete1.seasonBest = "1.87";
        athlete1.finalRank = 1;
        athlete1.bestHeight = "1.85";
        athlete1.status = "active";
        m_athleteData.append(athlete1);
        
        CsvAthleteData athlete2;
        athlete2.id = 2;
        athlete2.competitionId = competitionId;
        athlete2.name = "李四";
        athlete2.team = "市队";
        athlete2.bibNumber = 102;
        athlete2.personalBest = "1.85";
        athlete2.seasonBest = "1.82";
        athlete2.finalRank = 2;
        athlete2.bestHeight = "1.80";
        athlete2.status = "active";
        m_athleteData.append(athlete2);
        
        // Load attempt data (placeholder)
        m_attemptData.clear();
        
        // Athlete 1 attempts
        for (int i = 0; i < 7; ++i) {
            CsvAttemptData attempt;
            attempt.id = i + 1;
            attempt.athleteId = 1;
            attempt.height = m_heights[i];
            attempt.attemptNumber = 1;
            attempt.result = (i < 5) ? "success" : (i == 5) ? "failure" : "pass";
            attempt.timestamp = QDateTime::currentDateTime().addSecs(-3600 + i * 300).toString(Qt::ISODate);
            m_attemptData.append(attempt);
        }
        
        // Athlete 2 attempts
        for (int i = 0; i < 5; ++i) {
            CsvAttemptData attempt;
            attempt.id = i + 8;
            attempt.athleteId = 2;
            attempt.height = m_heights[i];
            attempt.attemptNumber = 1;
            attempt.result = (i < 4) ? "success" : "failure";
            attempt.timestamp = QDateTime::currentDateTime().addSecs(-3600 + i * 300).toString(Qt::ISODate);
            m_attemptData.append(attempt);
        }
        
        return true;
        
    } catch (const std::exception &e) {
        m_lastError = tr("加载比赛数据失败: %1").arg(e.what());
        return false;
    }
}

bool CsvExporter::writeCompetitionSheet(QTextStream &stream)
{
    stream << "# Competition Information" << CSV_NEWLINE;
    
    QStringList headers = {"Field", "Value"};
    stream << formatCsvRow(headers) << CSV_NEWLINE;
    
    stream << formatCsvRow({"ID", QString::number(m_competitionData.id)}) << CSV_NEWLINE;
    stream << formatCsvRow({"Name", m_competitionData.name}) << CSV_NEWLINE;
    stream << formatCsvRow({"Date", m_competitionData.date}) << CSV_NEWLINE;
    stream << formatCsvRow({"Venue", m_competitionData.venue}) << CSV_NEWLINE;
    stream << formatCsvRow({"Category", m_competitionData.category}) << CSV_NEWLINE;
    stream << formatCsvRow({"Status", m_competitionData.status}) << CSV_NEWLINE;
    
    return true;
}

bool CsvExporter::writeAthletesSheet(QTextStream &stream)
{
    stream << "# Athletes Data" << CSV_NEWLINE;
    
    QStringList headers = {"ID", "Competition_ID", "Name", "Team", "Bib_Number", 
                          "Personal_Best", "Season_Best", "Final_Rank", "Best_Height", "Status"};
    stream << formatCsvRow(headers) << CSV_NEWLINE;
    
    for (const CsvAthleteData &athlete : m_athleteData) {
        QStringList row = {
            QString::number(athlete.id),
            QString::number(athlete.competitionId),
            athlete.name,
            athlete.team,
            QString::number(athlete.bibNumber),
            athlete.personalBest,
            athlete.seasonBest,
            QString::number(athlete.finalRank),
            athlete.bestHeight,
            athlete.status
        };
        stream << formatCsvRow(row) << CSV_NEWLINE;
    }
    
    return true;
}

bool CsvExporter::writeAttemptsSheet(QTextStream &stream)
{
    stream << "# Attempt Records" << CSV_NEWLINE;
    
    QStringList headers = {"ID", "Athlete_ID", "Height", "Attempt_Number", "Result", "Timestamp"};
    stream << formatCsvRow(headers) << CSV_NEWLINE;
    
    for (const CsvAttemptData &attempt : m_attemptData) {
        QStringList row = {
            QString::number(attempt.id),
            QString::number(attempt.athleteId),
            attempt.height,
            QString::number(attempt.attemptNumber),
            attempt.result,
            formatTimestamp(attempt.timestamp)
        };
        stream << formatCsvRow(row) << CSV_NEWLINE;
    }
    
    return true;
}

bool CsvExporter::writeHeightsSheet(QTextStream &stream)
{
    stream << "# Heights Sequence" << CSV_NEWLINE;
    
    QStringList headers = {"Order", "Height"};
    stream << formatCsvRow(headers) << CSV_NEWLINE;
    
    for (int i = 0; i < m_heights.size(); ++i) {
        QStringList row = {QString::number(i + 1), m_heights[i]};
        stream << formatCsvRow(row) << CSV_NEWLINE;
    }
    
    return true;
}

bool CsvExporter::writeSummarySheet(QTextStream &stream)
{
    stream << "# Results Summary" << CSV_NEWLINE;
    
    QStringList headers = {"Rank", "Name", "Team", "Bib", "Best_Height"};
    
    // Add height columns
    for (const QString &height : m_heights) {
        headers.append(height);
    }
    
    stream << formatCsvRow(headers) << CSV_NEWLINE;
    
    // Sort athletes by rank
    QList<CsvAthleteData> sortedAthletes = m_athleteData;
    std::sort(sortedAthletes.begin(), sortedAthletes.end(), 
              [](const CsvAthleteData &a, const CsvAthleteData &b) {
                  return a.finalRank < b.finalRank;
              });
    
    for (const CsvAthleteData &athlete : sortedAthletes) {
        QStringList row = {
            QString::number(athlete.finalRank),
            athlete.name,
            athlete.team,
            QString::number(athlete.bibNumber),
            athlete.bestHeight
        };
        
        // Add attempt results for each height
        for (const QString &height : m_heights) {
            QString result = "-";
            
            // Find attempts for this athlete and height
            for (const CsvAttemptData &attempt : m_attemptData) {
                if (attempt.athleteId == athlete.id && attempt.height == height) {
                    if (attempt.result == "success") {
                        result = "O";
                    } else if (attempt.result == "failure") {
                        result = "X";
                    } else if (attempt.result == "pass") {
                        result = "-";
                    }
                    break;
                }
            }
            
            row.append(result);
        }
        
        stream << formatCsvRow(row) << CSV_NEWLINE;
    }
    
    return true;
}

QString CsvExporter::escapeCsvField(const QString &field) const
{
    QString escaped = field;
    
    // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
    if (escaped.contains(CSV_SEPARATOR) || escaped.contains(CSV_QUOTE) || escaped.contains('\n')) {
        escaped.replace(CSV_QUOTE, CSV_QUOTE + CSV_QUOTE); // Escape quotes by doubling
        escaped = CSV_QUOTE + escaped + CSV_QUOTE;
    }
    
    return escaped;
}

QString CsvExporter::formatCsvRow(const QStringList &fields) const
{
    QStringList escapedFields;
    for (const QString &field : fields) {
        escapedFields.append(escapeCsvField(field));
    }
    return escapedFields.join(CSV_SEPARATOR);
}

QString CsvExporter::formatTimestamp(const QString &timestamp) const
{
    QDateTime dt = QDateTime::fromString(timestamp, Qt::ISODate);
    if (dt.isValid()) {
        return dt.toString("yyyy-MM-dd hh:mm:ss");
    }
    return timestamp;
}

bool CsvExporter::validateCompetitionData() const
{
    if (m_competitionData.id <= 0) {
        const_cast<CsvExporter*>(this)->m_lastError = tr("无效的比赛ID");
        return false;
    }
    
    if (m_competitionData.name.isEmpty()) {
        const_cast<CsvExporter*>(this)->m_lastError = tr("比赛名称不能为空");
        return false;
    }
    
    return true;
}

bool CsvExporter::validateAthleteData() const
{
    if (m_athleteData.isEmpty()) {
        const_cast<CsvExporter*>(this)->m_lastError = tr("没有找到运动员数据");
        return false;
    }
    
    for (const CsvAthleteData &athlete : m_athleteData) {
        if (athlete.name.isEmpty()) {
            const_cast<CsvExporter*>(this)->m_lastError = tr("运动员姓名不能为空");
            return false;
        }
        
        if (athlete.bibNumber <= 0) {
            const_cast<CsvExporter*>(this)->m_lastError = tr("无效的运动员号码");
            return false;
        }
    }
    
    return true;
}

bool CsvExporter::validateAttemptData() const
{
    // Attempt data can be empty (no attempts recorded yet)
    // But if present, should be valid
    
    for (const CsvAttemptData &attempt : m_attemptData) {
        if (attempt.athleteId <= 0) {
            const_cast<CsvExporter*>(this)->m_lastError = tr("无效的运动员ID在试跳记录中");
            return false;
        }
        
        if (attempt.height.isEmpty()) {
            const_cast<CsvExporter*>(this)->m_lastError = tr("试跳高度不能为空");
            return false;
        }
        
        if (attempt.attemptNumber < 1 || attempt.attemptNumber > 3) {
            const_cast<CsvExporter*>(this)->m_lastError = tr("无效的试跳次数");
            return false;
        }
    }
    
    return true;
}
