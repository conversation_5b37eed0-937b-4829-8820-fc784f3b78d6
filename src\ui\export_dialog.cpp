#include "export_dialog.h"
#include "../utils/export_manager.h"

ExportDialog::ExportDialog(int competitionId, QWidget *parent)
    : QDialog(parent)
    , m_competitionId(competitionId)
    , m_exportManager(nullptr)
    , m_exportInProgress(false)
{
    setWindowTitle(tr("导出成绩"));
    setModal(true);
    setFixedSize(450, 350);
    
    setupUI();
    setupConnections();
    
    // Initialize export manager
    m_exportManager = new ExportManager(this);
    connect(m_exportManager, &ExportManager::progressUpdated,
            this, &ExportDialog::onExportProgress);
    connect(m_exportManager, &ExportManager::exportCompleted,
            this, &ExportDialog::onExportCompleted);
}

ExportDialog::~ExportDialog()
{
    // ExportManager will be deleted by Qt parent-child system
}

void ExportDialog::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(15);
    m_mainLayout->setContentsMargins(20, 20, 20, 20);
    
    // Format selection group
    m_formatGroup = new QGroupBox(tr("导出格式"), this);
    QVBoxLayout *formatLayout = new QVBoxLayout(m_formatGroup);
    
    m_pdfCheckBox = new QCheckBox(tr("PDF报告 (专业格式)"), this);
    m_csvCheckBox = new QCheckBox(tr("CSV数据 (结构化数据)"), this);
    
    // Default to both formats selected
    m_pdfCheckBox->setChecked(true);
    m_csvCheckBox->setChecked(true);
    
    formatLayout->addWidget(m_pdfCheckBox);
    formatLayout->addWidget(m_csvCheckBox);
    
    // Location selection group
    m_locationGroup = new QGroupBox(tr("保存位置"), this);
    QHBoxLayout *locationLayout = new QHBoxLayout(m_locationGroup);
    
    m_pathLineEdit = new QLineEdit(this);
    m_pathLineEdit->setText(getDefaultExportPath());
    m_pathLineEdit->setReadOnly(true);
    
    m_browseButton = new QPushButton(tr("浏览..."), this);
    m_browseButton->setFixedWidth(80);
    
    locationLayout->addWidget(m_pathLineEdit);
    locationLayout->addWidget(m_browseButton);
    
    // Progress group
    m_progressGroup = new QGroupBox(tr("导出进度"), this);
    QVBoxLayout *progressLayout = new QVBoxLayout(m_progressGroup);
    
    m_progressBar = new QProgressBar(this);
    m_progressBar->setVisible(false);
    
    m_statusLabel = new QLabel(tr("准备导出..."), this);
    m_statusLabel->setStyleSheet("color: #666;");
    
    progressLayout->addWidget(m_progressBar);
    progressLayout->addWidget(m_statusLabel);
    
    // Buttons
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    
    m_exportButton = new QPushButton(tr("开始导出"), this);
    m_exportButton->setDefault(true);
    m_exportButton->setMinimumWidth(100);
    
    m_cancelButton = new QPushButton(tr("取消"), this);
    m_cancelButton->setMinimumWidth(100);
    
    buttonLayout->addWidget(m_exportButton);
    buttonLayout->addWidget(m_cancelButton);
    
    // Add all groups to main layout
    m_mainLayout->addWidget(m_formatGroup);
    m_mainLayout->addWidget(m_locationGroup);
    m_mainLayout->addWidget(m_progressGroup);
    m_mainLayout->addStretch();
    m_mainLayout->addLayout(buttonLayout);
}

void ExportDialog::setupConnections()
{
    connect(m_pdfCheckBox, &QCheckBox::toggled, this, &ExportDialog::updateExportButton);
    connect(m_csvCheckBox, &QCheckBox::toggled, this, &ExportDialog::updateExportButton);
    connect(m_browseButton, &QPushButton::clicked, this, &ExportDialog::onBrowseClicked);
    connect(m_exportButton, &QPushButton::clicked, this, &ExportDialog::onExportClicked);
    connect(m_cancelButton, &QPushButton::clicked, this, &ExportDialog::onCancelClicked);
}

QString ExportDialog::getDefaultExportPath() const
{
    QString documentsPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString exportDir = QDir(documentsPath).filePath("HighJumpScorer_Exports");
    
    // Create directory if it doesn't exist
    QDir().mkpath(exportDir);
    
    return exportDir;
}

bool ExportDialog::validateInputs() const
{
    if (!m_pdfCheckBox->isChecked() && !m_csvCheckBox->isChecked()) {
        QMessageBox::warning(const_cast<ExportDialog*>(this), 
                           tr("输入错误"), 
                           tr("请至少选择一种导出格式。"));
        return false;
    }
    
    QString path = m_pathLineEdit->text().trimmed();
    if (path.isEmpty()) {
        QMessageBox::warning(const_cast<ExportDialog*>(this), 
                           tr("输入错误"), 
                           tr("请选择保存位置。"));
        return false;
    }
    
    QDir dir(path);
    if (!dir.exists()) {
        QMessageBox::warning(const_cast<ExportDialog*>(this), 
                           tr("路径错误"), 
                           tr("选择的保存路径不存在。"));
        return false;
    }
    
    return true;
}

void ExportDialog::onBrowseClicked()
{
    QString currentPath = m_pathLineEdit->text();
    QString selectedPath = QFileDialog::getExistingDirectory(
        this, 
        tr("选择导出目录"), 
        currentPath,
        QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks
    );
    
    if (!selectedPath.isEmpty()) {
        m_pathLineEdit->setText(selectedPath);
    }
}

void ExportDialog::onExportClicked()
{
    if (!validateInputs()) {
        return;
    }
    
    if (m_exportInProgress) {
        return;
    }
    
    m_exportInProgress = true;
    
    // Update UI for export state
    m_exportButton->setEnabled(false);
    m_pdfCheckBox->setEnabled(false);
    m_csvCheckBox->setEnabled(false);
    m_browseButton->setEnabled(false);
    m_pathLineEdit->setEnabled(false);
    
    m_progressBar->setVisible(true);
    m_progressBar->setValue(0);
    m_statusLabel->setText(tr("正在导出..."));
    
    emit exportStarted();
    
    // Start export process
    QString exportPath = m_pathLineEdit->text();
    bool exportPdf = m_pdfCheckBox->isChecked();
    bool exportCsv = m_csvCheckBox->isChecked();
    
    m_exportManager->startExport(m_competitionId, exportPath, exportPdf, exportCsv);
}

void ExportDialog::onCancelClicked()
{
    if (m_exportInProgress) {
        // TODO: Implement export cancellation if needed
        QMessageBox::information(this, tr("提示"), tr("导出正在进行中，请等待完成。"));
        return;
    }
    
    reject();
}

void ExportDialog::updateExportButton()
{
    bool hasSelection = m_pdfCheckBox->isChecked() || m_csvCheckBox->isChecked();
    m_exportButton->setEnabled(hasSelection && !m_exportInProgress);
}

void ExportDialog::onExportProgress(int current, int total)
{
    if (total > 0) {
        int percentage = (current * 100) / total;
        m_progressBar->setValue(percentage);
        m_statusLabel->setText(tr("导出进度: %1/%2").arg(current).arg(total));
    }
}

void ExportDialog::onExportCompleted(bool success, const QString &message)
{
    m_exportInProgress = false;
    
    // Reset UI state
    m_exportButton->setEnabled(true);
    m_pdfCheckBox->setEnabled(true);
    m_csvCheckBox->setEnabled(true);
    m_browseButton->setEnabled(true);
    m_pathLineEdit->setEnabled(true);
    
    if (success) {
        m_progressBar->setValue(100);
        m_statusLabel->setText(tr("导出完成"));
        m_statusLabel->setStyleSheet("color: green;");
        
        QMessageBox::information(this, tr("导出成功"), message);
        accept();
    } else {
        m_progressBar->setVisible(false);
        m_statusLabel->setText(tr("导出失败"));
        m_statusLabel->setStyleSheet("color: red;");
        
        QMessageBox::critical(this, tr("导出失败"), message);
    }
    
    emit exportFinished(success, message);
}
