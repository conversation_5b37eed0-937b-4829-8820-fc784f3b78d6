# Story 1.7: 最终成绩导出

## Status
Draft

## Epic
Epic 1: MVP核心功能 - 基础跳高计分系统

## Story Points
5

## Priority
Medium

## Summary
实现最终成绩导出功能，允许记分员在比赛结束后将官方成绩单导出为专业格式的PDF和结构化的CSV文件，确保数据来源于本地SQLite数据库并与计分表格状态完全一致。

## User Story
**作为一个** 记分员, **我想要** 在比赛完全结束后，能将最终的官方成绩单导出为PDF和CSV文件, **以便于** 我能进行成绩归档和分发。

## Acceptance Criteria
1. 界面上提供一个"导出成绩"的按钮。
2. 导出的数据**必须**来源于**本地SQLite数据库**。
3. 导出的PDF格式清晰、专业。
4. 导出的CSV文件包含所有结构化的原始数据。
5. 导出的两种文件中的数据都与计分表格的最终状态完全一致。

## Tasks / Subtasks
- [ ] 实现导出UI组件 (AC: 1)
  - [ ] 创建ExportDialog类提供导出选项界面
  - [ ] 添加"导出成绩"按钮到主窗口工具栏
  - [ ] 实现文件保存对话框集成
  - [ ] 添加导出进度指示器
- [ ] 实现PDF报告生成器 (AC: 2, 3, 5)
  - [ ] 创建PdfReportGenerator类处理PDF生成
  - [ ] 设计专业的PDF布局模板
  - [ ] 实现运动员成绩表格渲染
  - [ ] 添加比赛信息头部和页脚
  - [ ] 集成QPrinter和QPainter进行PDF输出
- [ ] 实现CSV数据导出器 (AC: 2, 4, 5)
  - [ ] 创建CsvExporter类处理结构化数据导出
  - [ ] 实现完整的试跳记录数据提取
  - [ ] 添加运动员信息和比赛元数据
  - [ ] 确保CSV格式符合标准规范
- [ ] 数据一致性验证 (AC: 5)
  - [ ] 实现导出前数据完整性检查
  - [ ] 添加计分表格状态与数据库同步验证
  - [ ] 创建数据校验和生成机制
  - [ ] 实现导出后数据验证报告
- [ ] 错误处理和用户反馈 (AC: 1, 2, 3, 4)
  - [ ] 添加文件写入权限检查
  - [ ] 实现导出失败错误处理
  - [ ] 提供用户友好的错误消息
  - [ ] 添加导出成功确认通知
- [ ] 单元测试和集成测试
  - [ ] 测试PDF生成功能和格式正确性
  - [ ] 测试CSV导出数据完整性
  - [ ] 测试导出UI交互流程
  - [ ] 测试错误处理和边界条件
  - [ ] 测试大数据量导出性能

## Dev Notes

### Previous Story Insights
从Story 1.6的实现中学到的关键经验：
- 使用事务确保数据一致性，导出操作应在数据库事务中读取最终状态
- 信号槽机制实现松耦合，导出进度更新应通过信号通知UI层
- 单例模式的线程安全实现，确保数据库访问的线程安全
- 错误处理和用户反馈的重要性，特别是文件操作相关的错误处理

### Data Models
**Competition模型** [Source: architecture/data-models.md#Competition]:
```cpp
class Competition {
public:
    int id() const;
    QString name() const;
    QDate date() const;
    QString venue() const;
    QString category() const;
    QList<Athlete> athletes() const;
    QList<Height> heights() const;
};
```

**Athlete模型** [Source: architecture/data-models.md#Athlete]:
```cpp
class Athlete {
public:
    int id() const;
    QString name() const;
    QString team() const;
    int bibNumber() const;
    QString personalBest() const;
    QString seasonBest() const;
    int finalRank() const;
    QString bestHeight() const;
};
```

**AttemptRecord模型** [Source: architecture/data-models.md#AttemptRecord]:
```cpp
class AttemptRecord {
public:
    enum Result { Success, Failure, Pass, Retired };
    
    int id() const;
    int athleteId() const;
    QString height() const;
    int attemptNumber() const;
    Result result() const;
    QDateTime timestamp() const;
};
```

### Database Schema
**competitions表结构** [Source: architecture/database-schema.md#competitions]:
```sql
CREATE TABLE competitions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    date DATE NOT NULL,
    venue TEXT NOT NULL,
    category TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'active'
);
```

**athletes表结构** [Source: architecture/database-schema.md#athletes]:
```sql
CREATE TABLE athletes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    competition_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    team TEXT,
    bib_number INTEGER NOT NULL,
    personal_best TEXT,
    season_best TEXT,
    final_rank INTEGER,
    best_height TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    FOREIGN KEY (competition_id) REFERENCES competitions(id)
);
```

**attempt_records表结构** [Source: architecture/database-schema.md#attempt_records]:
```sql
CREATE TABLE attempt_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    athlete_id INTEGER NOT NULL,
    height TEXT NOT NULL,
    attempt_number INTEGER NOT NULL,
    result TEXT NOT NULL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (athlete_id) REFERENCES athletes(id),
    CHECK (result IN ('success', 'failure', 'pass', 'retired')),
    CHECK (attempt_number BETWEEN 1 AND 3)
);
```

**DatabaseManager导出相关方法** [Source: architecture/database-schema.md#DatabaseManager]:
- `Competition getCompetitionById(int id)` - 获取比赛完整信息
- `QList<Athlete> getAthletesByCompetition(int competitionId)` - 获取比赛运动员列表
- `QList<AttemptRecord> getAttemptsByAthlete(int athleteId)` - 获取运动员试跳记录
- `QList<Height> getHeightsByCompetition(int competitionId)` - 获取比赛高度序列

### File Locations
基于项目结构 [Source: architecture/unified-project-structure.md]:
- 导出对话框: `src/ui/export_dialog.h/cpp`
- PDF报告生成器: `src/utils/pdf_report_generator.h/cpp`
- CSV导出器: `src/utils/csv_exporter.h/cpp`
- 导出管理器: `src/utils/export_manager.h/cpp`
- 数据库访问: `src/persistence/database_manager.h/cpp` (扩展现有方法)
- 单元测试: `tests/unit/test_export_manager.cpp`, `tests/unit/test_pdf_generator.cpp`
- 集成测试: `tests/integration/test_export_integration.cpp`

### Technical Constraints
**技术栈要求** [Source: architecture/tech-stack.md]:
- Qt 6.9.1框架，使用QPrinter和QPainter进行PDF生成
- C++17标准，利用现代C++特性进行文件操作
- SQLite3数据库，确保数据来源的一致性
- Qt Test框架进行单元测试

**PDF生成要求** [Source: architecture/tech-stack.md]:
- 使用Qt内置的QPrinter类，不依赖外部PDF库
- 支持A4纸张格式，300 DPI分辨率
- 包含比赛标题、日期、地点等元信息
- 表格格式清晰，支持多页输出

**文件操作规则** [Source: architecture/coding-standards.md]:
- 所有文件操作必须包含错误检查和异常处理
- 使用QStandardPaths获取合适的保存目录
- 文件名必须包含时间戳避免冲突
- 导出操作必须在后台线程执行，避免UI阻塞

### Testing Requirements
**测试策略** [Source: architecture/testing-strategy.md]:
- `testPdfGeneration()` - PDF生成功能测试
- `testCsvExport()` - CSV导出数据完整性测试
- `testDataConsistency()` - 数据一致性验证测试
- `testFilePermissions()` - 文件权限和错误处理测试
- `testLargeDataExport()` - 大数据量导出性能测试
- `testExportDialog()` - 导出对话框UI交互测试
- `testProgressIndicator()` - 进度指示器功能测试

**Mock对象设计** [Source: architecture/testing-strategy.md]:
- `MockDatabaseManager` - 模拟数据库管理器
- `MockFileSystem` - 模拟文件系统操作
- 模拟大量运动员和试跳记录数据

## Testing

### Testing Standards
**测试文件位置**: 
- `tests/unit/test_export_manager.cpp` - 导出管理器单元测试
- `tests/unit/test_pdf_generator.cpp` - PDF生成器单元测试
- `tests/unit/test_csv_exporter.cpp` - CSV导出器单元测试
- `tests/integration/test_export_integration.cpp` - 导出功能集成测试

**测试框架**: Qt Test framework

**测试模式**: 
- 单元测试覆盖核心导出逻辑
- 集成测试验证完整的导出流程
- Mock测试模拟数据库和文件系统

**具体测试要求**:
- 测试PDF生成的格式正确性和内容完整性
- 验证CSV导出的数据结构和编码正确性
- 测试导出UI的用户交互流程
- 验证错误处理和边界条件
- 测试大数据量导出的性能表现
- 确保导出数据与数据库状态的一致性

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-07 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used
[To be filled by Dev Agent]

### Debug Log References
[To be filled by Dev Agent]

### Completion Notes List
[To be filled by Dev Agent]

### File List
[To be filled by Dev Agent]

## QA Results
[To be filled by QA Agent]
