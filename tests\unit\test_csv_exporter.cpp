#include <QtTest>
#include <QObject>
#include <QTemporaryDir>
#include <QFileInfo>
#include <QFile>
#include <QTextStream>

#include "../../src/utils/csv_exporter.h"

class TestCsvExporter : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Test cases
    void testConstructor();
    void testExportDataValidInput();
    void testExportDataInvalidCompetitionId();
    void testExportDataInvalidPath();
    void testCsvFileCreation();
    void testCsvFileContent();
    void testCsvFormatting();
    void testDataIntegrity();
    void testErrorHandling();
    void testLastErrorMessage();

private:
    CsvExporter *m_exporter;
    QTemporaryDir *m_tempDir;
    QString m_validPath;
    int m_testCompetitionId;
    
    bool validateCsvFormat(const QString &filePath);
    QStringList readCsvFile(const QString &filePath);
};

void TestCsvExporter::initTestCase()
{
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    m_validPath = m_tempDir->path();
    m_testCompetitionId = 1;
}

void TestCsvExporter::cleanupTestCase()
{
    delete m_tempDir;
}

void TestCsvExporter::init()
{
    m_exporter = new CsvExporter(this);
    QVERIFY(m_exporter != nullptr);
}

void TestCsvExporter::cleanup()
{
    delete m_exporter;
    m_exporter = nullptr;
}

void TestCsvExporter::testConstructor()
{
    QVERIFY(m_exporter != nullptr);
    QVERIFY(m_exporter->lastError().isEmpty());
}

void TestCsvExporter::testExportDataValidInput()
{
    QString outputPath = QDir(m_validPath).filePath("test_export.csv");
    
    bool result = m_exporter->exportData(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    QVERIFY(m_exporter->lastError().isEmpty());
}

void TestCsvExporter::testExportDataInvalidCompetitionId()
{
    QString outputPath = QDir(m_validPath).filePath("test_invalid_id.csv");
    
    bool result = m_exporter->exportData(-1, outputPath);
    
    // Should handle invalid competition ID
    if (!result) {
        QVERIFY(!m_exporter->lastError().isEmpty());
    }
}

void TestCsvExporter::testExportDataInvalidPath()
{
    QString invalidPath = "/invalid/nonexistent/directory/test.csv";
    
    bool result = m_exporter->exportData(m_testCompetitionId, invalidPath);
    
    QVERIFY(!result);
    QVERIFY(!m_exporter->lastError().isEmpty());
}

void TestCsvExporter::testCsvFileCreation()
{
    QString outputPath = QDir(m_validPath).filePath("creation_test.csv");
    
    // Ensure file doesn't exist before test
    QFile::remove(outputPath);
    QVERIFY(!QFile::exists(outputPath));
    
    bool result = m_exporter->exportData(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    QVERIFY(QFile::exists(outputPath));
    
    QFileInfo fileInfo(outputPath);
    QVERIFY(fileInfo.isFile());
    QVERIFY(fileInfo.isReadable());
    QVERIFY(fileInfo.size() > 0);
}

void TestCsvExporter::testCsvFileContent()
{
    QString outputPath = QDir(m_validPath).filePath("content_test.csv");
    
    bool result = m_exporter->exportData(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    
    QStringList lines = readCsvFile(outputPath);
    QVERIFY(lines.size() > 0);
    
    // Check for expected sections
    QString content = lines.join("\n");
    QVERIFY(content.contains("Competition Information"));
    QVERIFY(content.contains("Athletes Data"));
    QVERIFY(content.contains("Attempt Records"));
    QVERIFY(content.contains("Heights Sequence"));
    QVERIFY(content.contains("Results Summary"));
}

void TestCsvExporter::testCsvFormatting()
{
    QString outputPath = QDir(m_validPath).filePath("format_test.csv");
    
    bool result = m_exporter->exportData(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    QVERIFY(validateCsvFormat(outputPath));
}

void TestCsvExporter::testDataIntegrity()
{
    QString outputPath = QDir(m_validPath).filePath("integrity_test.csv");
    
    bool result = m_exporter->exportData(m_testCompetitionId, outputPath);
    
    QVERIFY(result);
    
    QStringList lines = readCsvFile(outputPath);
    
    // Find athletes data section
    bool inAthletesSection = false;
    int athleteCount = 0;
    
    for (const QString &line : lines) {
        if (line.contains("Athletes Data")) {
            inAthletesSection = true;
            continue;
        }
        
        if (inAthletesSection && line.startsWith("#")) {
            inAthletesSection = false;
            continue;
        }
        
        if (inAthletesSection && !line.isEmpty() && !line.startsWith("ID,")) {
            athleteCount++;
        }
    }
    
    // Should have at least some test data
    QVERIFY(athleteCount >= 0); // Might be 0 for empty competition
}

void TestCsvExporter::testErrorHandling()
{
    // Test with read-only directory
    QString readOnlyPath = QDir(m_validPath).filePath("readonly");
    QDir().mkdir(readOnlyPath);
    
    QFile::setPermissions(readOnlyPath, QFile::ReadOwner | QFile::ReadGroup | QFile::ReadOther);
    
    QString outputPath = QDir(readOnlyPath).filePath("test.csv");
    
    bool result = m_exporter->exportData(m_testCompetitionId, outputPath);
    
    QVERIFY(!result);
    QVERIFY(!m_exporter->lastError().isEmpty());
    
    // Restore permissions
    QFile::setPermissions(readOnlyPath, QFile::WriteOwner | QFile::ReadOwner);
}

void TestCsvExporter::testLastErrorMessage()
{
    // Initially no error
    QVERIFY(m_exporter->lastError().isEmpty());
    
    // Successful export
    QString validPath = QDir(m_validPath).filePath("success_test.csv");
    bool result = m_exporter->exportData(m_testCompetitionId, validPath);
    
    if (result) {
        QVERIFY(m_exporter->lastError().isEmpty());
    }
    
    // Failed export
    QString invalidPath = "/invalid/path/test.csv";
    result = m_exporter->exportData(m_testCompetitionId, invalidPath);
    
    QVERIFY(!result);
    QVERIFY(!m_exporter->lastError().isEmpty());
    
    QString errorMsg = m_exporter->lastError();
    QVERIFY(errorMsg.length() > 5);
}

bool TestCsvExporter::validateCsvFormat(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream in(&file);
    in.setEncoding(QStringConverter::Utf8);
    
    while (!in.atEnd()) {
        QString line = in.readLine();
        
        // Skip comments and empty lines
        if (line.startsWith("#") || line.isEmpty()) {
            continue;
        }
        
        // Check for basic CSV structure
        if (line.contains(",")) {
            // Valid CSV line
            continue;
        } else if (line.contains("Information") || line.contains("Data") || 
                  line.contains("Records") || line.contains("Summary")) {
            // Section headers are OK
            continue;
        } else {
            // Unexpected format
            return false;
        }
    }
    
    return true;
}

QStringList TestCsvExporter::readCsvFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return QStringList();
    }
    
    QTextStream in(&file);
    in.setEncoding(QStringConverter::Utf8);
    
    QStringList lines;
    while (!in.atEnd()) {
        lines.append(in.readLine());
    }
    
    return lines;
}

QTEST_MAIN(TestCsvExporter)
#include "test_csv_exporter.moc"
